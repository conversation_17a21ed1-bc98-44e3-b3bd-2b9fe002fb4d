#!/bin/bash

# API Testing Script for Smart e-Permit Platform
# This script tests all API endpoints to ensure they're working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
API_BASE_URL="http://localhost:3000/api/v1"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="password123"
AUTH_TOKEN=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Function to make HTTP requests
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    
    if [ -n "$data" ]; then
        if [ -n "$AUTH_TOKEN" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $AUTH_TOKEN" \
                -d "$data" \
                "$API_BASE_URL$endpoint")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$API_BASE_URL$endpoint")
        fi
    else
        if [ -n "$AUTH_TOKEN" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Authorization: Bearer $AUTH_TOKEN" \
                "$API_BASE_URL$endpoint")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                "$API_BASE_URL$endpoint")
        fi
    fi
    
    # Split response and status code
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    if [ "$status" = "$expected_status" ]; then
        print_success "$method $endpoint - Status: $status"
        echo "$body"
        return 0
    else
        print_error "$method $endpoint - Expected: $expected_status, Got: $status"
        echo "$body"
        return 1
    fi
}

# Test health endpoint
test_health() {
    print_status "Testing health endpoint..."
    make_request "GET" "/health" "" "200"
    echo ""
}

# Test authentication (mock - since we're using Firebase)
test_auth() {
    print_status "Testing authentication endpoints..."
    
    # Note: In real scenario, you'd get token from Firebase
    # For testing, we'll use a mock token or skip auth tests
    print_warning "Authentication testing requires Firebase setup"
    print_warning "Skipping auth tests for now"
    echo ""
}

# Test permits endpoints
test_permits() {
    print_status "Testing permits endpoints..."
    
    # Get permits (should work without auth for testing)
    print_status "GET /permits"
    if make_request "GET" "/permits" "" "200" || make_request "GET" "/permits" "" "401"; then
        echo "Permits endpoint is responding"
    fi
    echo ""
    
    # Test permit creation (would need auth)
    print_status "POST /permits (without auth - should fail)"
    make_request "POST" "/permits" '{"title":"Test Permit","type":"HOT_WORK"}' "401"
    echo ""
}

# Test analytics endpoints
test_analytics() {
    print_status "Testing analytics endpoints..."
    
    print_status "GET /analytics/permits"
    if make_request "GET" "/analytics/permits" "" "200" || make_request "GET" "/analytics/permits" "" "401"; then
        echo "Analytics endpoint is responding"
    fi
    echo ""
}

# Test AI endpoints
test_ai() {
    print_status "Testing AI endpoints..."
    
    print_status "GET /ai/insights"
    if make_request "GET" "/ai/insights" "" "200" || make_request "GET" "/ai/insights" "" "401"; then
        echo "AI insights endpoint is responding"
    fi
    echo ""
}

# Test database connection
test_database() {
    print_status "Testing database connection..."
    
    # Check if we can connect to the database
    if command -v psql &> /dev/null; then
        if PGPASSWORD=dev_password_123 psql -h localhost -U epermit_user -d epermit_dev -c "SELECT 1;" &> /dev/null; then
            print_success "Database connection successful"
        else
            print_error "Database connection failed"
        fi
    else
        print_warning "psql not available, skipping database connection test"
    fi
    echo ""
}

# Test Redis connection
test_redis() {
    print_status "Testing Redis connection..."
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            print_success "Redis connection successful"
        else
            print_error "Redis connection failed"
        fi
    else
        print_warning "redis-cli not available, skipping Redis connection test"
    fi
    echo ""
}

# Check if backend server is running
check_server() {
    print_status "Checking if backend server is running..."
    
    if curl -s "$API_BASE_URL/health" &> /dev/null; then
        print_success "Backend server is running at $API_BASE_URL"
        return 0
    else
        print_error "Backend server is not running at $API_BASE_URL"
        print_status "Please start the backend server with: cd backend && npm run dev"
        return 1
    fi
}

# Main test function
main() {
    echo "🧪 Smart e-Permit Platform API Testing"
    echo "======================================"
    echo ""
    
    # Check if server is running
    if ! check_server; then
        exit 1
    fi
    echo ""
    
    # Run tests
    test_health
    test_database
    test_redis
    test_auth
    test_permits
    test_analytics
    test_ai
    
    print_success "🎉 API testing completed!"
    echo ""
    print_status "Note: Some tests may fail if authentication is not properly configured"
    print_status "This is expected for a fresh setup without Firebase credentials"
}

# Run main function
main "$@"
