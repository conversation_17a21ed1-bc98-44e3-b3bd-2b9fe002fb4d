import * as SQLite from 'expo-sqlite';
import { Permit, User, Site } from '@epermit/shared';

const DATABASE_NAME = 'epermit.db';
const DATABASE_VERSION = 1;

let db: SQLite.WebSQLDatabase;

export const initializeDatabase = async (): Promise<void> => {
  try {
    db = SQLite.openDatabase(DATABASE_NAME);
    
    await createTables();
    console.log('✅ Database initialized successfully');
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
};

const createTables = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    db.transaction(
      (tx) => {
        // Users table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            email TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            role TEXT NOT NULL,
            site_id TEXT,
            firebase_uid TEXT UNIQUE NOT NULL,
            is_active INTEGER DEFAULT 1,
            last_login_at TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            synced INTEGER DEFAULT 0
          );
        `);

        // Sites table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS sites (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            location TEXT NOT NULL,
            timezone TEXT NOT NULL,
            contact_email TEXT NOT NULL,
            contact_phone TEXT NOT NULL,
            is_active INTEGER DEFAULT 1,
            settings TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            synced INTEGER DEFAULT 0
          );
        `);

        // Permits table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS permits (
            id TEXT PRIMARY KEY,
            permit_number TEXT UNIQUE NOT NULL,
            title TEXT NOT NULL,
            description TEXT NOT NULL,
            type TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'DRAFT',
            risk_level TEXT NOT NULL,
            location TEXT NOT NULL,
            site_id TEXT NOT NULL,
            requested_by TEXT NOT NULL,
            approved_by TEXT,
            supervisor_id TEXT,
            start_date TEXT NOT NULL,
            end_date TEXT NOT NULL,
            actual_start_date TEXT,
            actual_end_date TEXT,
            ppe_required TEXT NOT NULL,
            safety_steps TEXT NOT NULL,
            isolation_required INTEGER DEFAULT 0,
            isolation_steps TEXT,
            emergency_contact TEXT NOT NULL,
            qr_code TEXT,
            voice_notes TEXT,
            attachments TEXT,
            ai_recommendations TEXT,
            approval_notes TEXT,
            rejection_reason TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            synced INTEGER DEFAULT 0,
            FOREIGN KEY (site_id) REFERENCES sites (id),
            FOREIGN KEY (requested_by) REFERENCES users (id),
            FOREIGN KEY (approved_by) REFERENCES users (id),
            FOREIGN KEY (supervisor_id) REFERENCES users (id)
          );
        `);

        // Voice notes table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS voice_notes (
            id TEXT PRIMARY KEY,
            permit_id TEXT NOT NULL,
            file_path TEXT NOT NULL,
            duration INTEGER NOT NULL,
            transcription TEXT,
            created_at TEXT NOT NULL,
            synced INTEGER DEFAULT 0,
            FOREIGN KEY (permit_id) REFERENCES permits (id)
          );
        `);

        // Attachments table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS attachments (
            id TEXT PRIMARY KEY,
            permit_id TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_name TEXT NOT NULL,
            file_type TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            created_at TEXT NOT NULL,
            synced INTEGER DEFAULT 0,
            FOREIGN KEY (permit_id) REFERENCES permits (id)
          );
        `);

        // Sync queue table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS sync_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            table_name TEXT NOT NULL,
            record_id TEXT NOT NULL,
            operation TEXT NOT NULL,
            data TEXT NOT NULL,
            created_at TEXT NOT NULL,
            attempts INTEGER DEFAULT 0,
            last_attempt_at TEXT,
            error_message TEXT
          );
        `);

        // Create indexes
        tx.executeSql('CREATE INDEX IF NOT EXISTS idx_permits_status ON permits (status);');
        tx.executeSql('CREATE INDEX IF NOT EXISTS idx_permits_site_id ON permits (site_id);');
        tx.executeSql('CREATE INDEX IF NOT EXISTS idx_permits_requested_by ON permits (requested_by);');
        tx.executeSql('CREATE INDEX IF NOT EXISTS idx_permits_synced ON permits (synced);');
        tx.executeSql('CREATE INDEX IF NOT EXISTS idx_voice_notes_permit_id ON voice_notes (permit_id);');
        tx.executeSql('CREATE INDEX IF NOT EXISTS idx_attachments_permit_id ON attachments (permit_id);');
        tx.executeSql('CREATE INDEX IF NOT EXISTS idx_sync_queue_table_record ON sync_queue (table_name, record_id);');
      },
      (error) => {
        console.error('Transaction error:', error);
        reject(error);
      },
      () => {
        console.log('Database tables created successfully');
        resolve();
      }
    );
  });
};

export const getDatabase = (): SQLite.WebSQLDatabase => {
  if (!db) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return db;
};

// Helper function to execute SQL queries
export const executeSql = (
  sql: string,
  params: any[] = []
): Promise<SQLite.SQLResultSet> => {
  return new Promise((resolve, reject) => {
    db.transaction(
      (tx) => {
        tx.executeSql(
          sql,
          params,
          (_, result) => resolve(result),
          (_, error) => {
            reject(error);
            return false;
          }
        );
      },
      (error) => reject(error)
    );
  });
};

// Helper function to get all records from a table
export const getAllRecords = async (tableName: string): Promise<any[]> => {
  const result = await executeSql(`SELECT * FROM ${tableName}`);
  return result.rows._array;
};

// Helper function to get a record by ID
export const getRecordById = async (tableName: string, id: string): Promise<any | null> => {
  const result = await executeSql(`SELECT * FROM ${tableName} WHERE id = ?`, [id]);
  return result.rows.length > 0 ? result.rows._array[0] : null;
};

// Helper function to insert a record
export const insertRecord = async (tableName: string, data: any): Promise<void> => {
  const columns = Object.keys(data).join(', ');
  const placeholders = Object.keys(data).map(() => '?').join(', ');
  const values = Object.values(data);
  
  await executeSql(
    `INSERT INTO ${tableName} (${columns}) VALUES (${placeholders})`,
    values
  );
};

// Helper function to update a record
export const updateRecord = async (tableName: string, id: string, data: any): Promise<void> => {
  const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
  const values = [...Object.values(data), id];
  
  await executeSql(
    `UPDATE ${tableName} SET ${setClause} WHERE id = ?`,
    values
  );
};

// Helper function to delete a record
export const deleteRecord = async (tableName: string, id: string): Promise<void> => {
  await executeSql(`DELETE FROM ${tableName} WHERE id = ?`, [id]);
};

// Helper function to clear all data (for testing)
export const clearAllData = async (): Promise<void> => {
  const tables = ['permits', 'voice_notes', 'attachments', 'users', 'sites', 'sync_queue'];
  
  for (const table of tables) {
    await executeSql(`DELETE FROM ${table}`);
  }
};

// Helper function to get unsynced records
export const getUnsyncedRecords = async (tableName: string): Promise<any[]> => {
  const result = await executeSql(`SELECT * FROM ${tableName} WHERE synced = 0`);
  return result.rows._array;
};

// Helper function to mark record as synced
export const markAsSynced = async (tableName: string, id: string): Promise<void> => {
  await executeSql(`UPDATE ${tableName} SET synced = 1 WHERE id = ?`, [id]);
};
