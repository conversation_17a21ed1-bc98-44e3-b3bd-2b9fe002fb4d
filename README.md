# 🏭 Smart e-Permit Platform

A production-ready digital permit management system for industrial MNCs with AI-powered features, offline-first mobile app, and comprehensive admin dashboard.

## 🎯 Key Features

- **Voice-based permit creation** for field technicians
- **AI-powered smart suggestions** for PPE and safety protocols
- **Offline-first mobile app** with auto-sync
- **Multi-site & multi-language support**
- **Real-time role-based notifications**
- **QR-based permit verification**
- **Analytics dashboard with risk heatmaps**

## 🏗️ Architecture

```
ePermit-Platform/
├── backend/           # Node.js + Express + PostgreSQL API
├── mobile-app/        # React Native (Expo) for field workers
├── web-admin/         # React + Vite admin dashboard
├── shared/            # Shared types and utilities
├── docker/            # Docker configurations
└── .github/           # CI/CD workflows
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Docker & Docker Compose
- Expo CLI

### Backend Setup
```bash
cd backend
npm install
cp .env.example .env
# Configure your database and Firebase credentials
npm run migrate
npm run dev
```

### Mobile App Setup
```bash
cd mobile-app
npm install
expo start
```

### Web Admin Setup
```bash
cd web-admin
npm install
cp .env.example .env
npm run dev
```

### Docker Setup (All Services)
```bash
docker-compose up -d
```

## 🧪 Testing

```bash
# Backend tests
cd backend && npm test

# Mobile app tests
cd mobile-app && npm test

# Web admin tests
cd web-admin && npm test

# Run all tests
npm run test:all
```

## 🚀 Deployment

### Backend (Render)
```bash
cd backend
npm run build
# Deploy to Render using GitHub integration
```

### Web Admin (Vercel)
```bash
cd web-admin
npm run build
# Deploy to Vercel using GitHub integration
```

### Mobile App (Expo EAS)
```bash
cd mobile-app
eas build --platform all
eas submit
```

## 📊 Tech Stack

- **Backend:** Node.js, Express, PostgreSQL, Sequelize
- **Mobile:** React Native, Expo, SQLite
- **Web:** React, Vite, Chart.js
- **Auth:** Firebase Authentication
- **AI:** Mock service (ready for OpenAI integration)
- **DevOps:** Docker, GitHub Actions, Render, Vercel

## 🔐 Security Features

- JWT-based authentication
- Role-based access control
- Input validation and sanitization
- Rate limiting
- Audit logging
- Secure QR code generation

## 📱 Mobile Features

- Offline-first architecture
- Voice-to-text permit creation
- Real-time sync when online
- QR code scanning
- Push notifications
- Multi-language support

## 🖥️ Admin Dashboard Features

- Permit approval workflows
- Analytics and KPI dashboards
- Risk heatmaps
- User management
- Audit trail viewing
- Bulk operations

## 🤖 AI Features

- Smart PPE recommendations
- Safety protocol suggestions
- Risk assessment scoring
- Auto-fill based on job type
- Compliance checking

## 📄 API Documentation

API documentation is available at `/api/docs` when running the backend server.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📝 License

MIT License - see LICENSE file for details.

## 🆘 Support

For support, email <EMAIL> or create an issue in this repository.
