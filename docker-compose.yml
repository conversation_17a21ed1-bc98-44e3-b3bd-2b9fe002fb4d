version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: epermit-postgres
    environment:
      POSTGRES_DB: epermit_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - epermit-network
    restart: unless-stopped

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: epermit-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - epermit-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: epermit-backend
    environment:
      NODE_ENV: development
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: epermit_dev
      DB_USERNAME: postgres
      DB_PASSWORD: password
      REDIS_URL: redis://redis:6379
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./shared:/shared
    depends_on:
      - postgres
      - redis
    networks:
      - epermit-network
    restart: unless-stopped
    command: npm run dev

  # Web Admin Dashboard
  web-admin:
    build:
      context: ./web-admin
      dockerfile: Dockerfile
    container_name: epermit-web-admin
    environment:
      VITE_API_BASE_URL: http://localhost:3000/api/v1
    ports:
      - "5173:5173"
    volumes:
      - ./web-admin:/app
      - /app/node_modules
      - ./shared:/shared
    depends_on:
      - backend
    networks:
      - epermit-network
    restart: unless-stopped
    command: npm run dev

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: epermit-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - web-admin
    networks:
      - epermit-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  epermit-network:
    driver: bridge
