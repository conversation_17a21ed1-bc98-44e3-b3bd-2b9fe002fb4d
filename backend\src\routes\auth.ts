import { Router } from 'express';
import {
  getCurrentUser,
  updateProfile,
  registerUser,
  getUserPermissions,
} from '../controllers/authController';
import { authenticateToken, requireRole } from '../middleware/auth';
import { validateUserCreation, validateUserUpdate } from '../middleware/validation';
import { auditLog, auditMiddleware } from '../middleware/audit';
import { authLimiter } from '../middleware/rateLimiter';

const router = Router();

// Apply auth rate limiting to all auth routes
router.use(authLimiter);

// Get current user profile
router.get('/me', authenticateToken, getCurrentUser);

// Update user profile
router.put(
  '/me',
  authenticateToken,
  validateUserUpdate,
  auditLog('User', 'UPDATE_PROFILE'),
  auditMiddleware,
  updateProfile
);

// Register new user (admin only)
router.post(
  '/register',
  authenticateToken,
  requireRole(['ADMIN']),
  validateUserCreation,
  auditLog('User', 'CREATE_USER'),
  auditMiddleware,
  registerUser
);

// Get user permissions
router.get('/permissions', authenticateToken, getUserPermissions);

export default router;
