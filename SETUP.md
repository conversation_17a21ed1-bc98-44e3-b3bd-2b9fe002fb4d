# Smart e-Permit Platform Setup Guide

This guide will help you set up the Smart e-Permit Platform for development and production.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** 18+ and npm/yarn
- **PostgreSQL** 14+
- **Git**
- **Docker** and Docker Compose (optional, for containerized setup)
- **Expo CLI** (for mobile development)

### Firebase Setup

1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication with Email/Password provider
3. Create a web app and note down the configuration
4. Generate a service account key for the backend

## 🚀 Quick Setup (Recommended)

### 1. Clone the Repository

```bash
git clone <repository-url>
cd smart-epermit-platform
```

### 2. Install Dependencies

```bash
# Install all dependencies for all packages
npm run setup
```

### 3. Environment Configuration

#### Backend Environment
```bash
cd backend
cp .env.example .env
```

Edit `backend/.env` with your configuration:
```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=epermit_dev
DB_USERNAME=postgres
DB_PASSWORD=your_password

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# JWT
JWT_SECRET=your-super-secret-jwt-key

# App
PORT=3000
NODE_ENV=development
```

#### Web Admin Environment
```bash
cd web-admin
cp .env.example .env.local
```

Edit `web-admin/.env.local`:
```env
VITE_API_BASE_URL=http://localhost:3000/api/v1
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=your-app-id
```

#### Mobile App Configuration
Update Firebase configuration in `mobile-app/src/contexts/AuthContext.tsx`:
```typescript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};
```

### 4. Database Setup

```bash
# Create PostgreSQL database
createdb epermit_dev

# Run database migrations
cd backend
npm run migrate

# Seed initial data (optional)
npm run seed
```

### 5. Start Development Servers

Open 3 terminal windows:

#### Terminal 1 - Backend API
```bash
cd backend
npm run dev
```
API will be available at http://localhost:3000

#### Terminal 2 - Web Admin Dashboard
```bash
cd web-admin
npm run dev
```
Dashboard will be available at http://localhost:5173

#### Terminal 3 - Mobile App
```bash
cd mobile-app
npm start
```
Follow Expo CLI instructions to run on device/simulator

## 🐳 Docker Setup (Alternative)

If you prefer using Docker:

### 1. Start with Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

This will start:
- PostgreSQL database on port 5432
- Redis on port 6379
- Backend API on port 3000
- Web Admin on port 5173

### 2. Initialize Database

```bash
# Run migrations
docker-compose exec backend npm run migrate

# Seed data
docker-compose exec backend npm run seed
```

## 🧪 Testing Setup

### Run All Tests
```bash
npm run test:all
```

### Run Individual Tests
```bash
# Backend tests
cd backend && npm test

# Web admin tests
cd web-admin && npm test

# Mobile app tests
cd mobile-app && npm test
```

## 📱 Mobile App Setup

### iOS Setup
1. Install Xcode from the App Store
2. Install iOS Simulator
3. Run `npm start` in mobile-app directory
4. Press `i` to open iOS simulator

### Android Setup
1. Install Android Studio
2. Set up Android Virtual Device (AVD)
3. Run `npm start` in mobile-app directory
4. Press `a` to open Android emulator

### Physical Device Testing
1. Install Expo Go app on your device
2. Run `npm start` in mobile-app directory
3. Scan QR code with Expo Go (Android) or Camera app (iOS)

## 🔧 Development Tools

### Code Quality
```bash
# Lint all packages
npm run lint:all

# Format code
npm run format:all

# Type checking
npm run type-check:all
```

### Database Management
```bash
cd backend

# Create new migration
npm run migrate:create -- --name add_new_table

# Run migrations
npm run migrate

# Rollback migration
npm run migrate:rollback

# Reset database
npm run db:reset
```

## 🚀 Production Deployment

### Backend Deployment

#### Option 1: Traditional Server
```bash
cd backend

# Build the application
npm run build

# Start production server
npm start

# Or use PM2 for process management
npm install -g pm2
pm2 start dist/server.js --name epermit-api
```

#### Option 2: Docker
```bash
# Build production image
docker build -t epermit-backend .

# Run container
docker run -p 3000:3000 --env-file .env epermit-backend
```

### Web Admin Deployment

#### Build for Production
```bash
cd web-admin
npm run build
```

#### Deploy to Static Hosting
- Upload `dist/` folder to your static hosting provider
- Configure your web server to serve `index.html` for all routes
- Set up environment variables for production

#### Deploy with Docker
```bash
# Build production image
docker build -t epermit-web-admin .

# Run container
docker run -p 80:80 epermit-web-admin
```

### Mobile App Deployment

#### Build for App Stores
```bash
cd mobile-app

# Install EAS CLI
npm install -g @expo/eas-cli

# Configure EAS
eas build:configure

# Build for iOS
eas build --platform ios

# Build for Android
eas build --platform android

# Submit to app stores
eas submit
```

## 🔐 Security Configuration

### Production Security Checklist

1. **Environment Variables**
   - Use strong, unique secrets for JWT_SECRET
   - Use production Firebase credentials
   - Set NODE_ENV=production

2. **Database Security**
   - Use strong database passwords
   - Enable SSL connections
   - Restrict database access by IP

3. **API Security**
   - Enable CORS for specific domains only
   - Set up rate limiting
   - Use HTTPS in production

4. **Firebase Security**
   - Configure Firebase security rules
   - Enable App Check for mobile app
   - Set up proper authentication flows

## 📊 Monitoring & Logging

### Application Monitoring
- Set up error tracking (Sentry, Bugsnag)
- Configure performance monitoring
- Set up uptime monitoring

### Logging
- Configure structured logging
- Set up log aggregation
- Monitor application metrics

## 🆘 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connection
psql -h localhost -U postgres -d epermit_dev
```

#### Port Already in Use
```bash
# Find process using port 3000
lsof -i :3000

# Kill process
kill -9 <PID>
```

#### Firebase Authentication Issues
- Verify Firebase configuration
- Check API keys and project settings
- Ensure Authentication is enabled in Firebase Console

#### Mobile App Issues
```bash
# Clear Expo cache
expo start -c

# Reset Metro bundler cache
npx react-native start --reset-cache
```

### Getting Help

1. Check the [Issues](https://github.com/your-org/smart-epermit-platform/issues) page
2. Review the documentation
3. Contact the development team

## 📝 Next Steps

After setup is complete:

1. **Create Admin User**: Use the seeded admin account or create one through Firebase
2. **Configure Sites**: Set up your industrial sites in the admin dashboard
3. **Add Users**: Invite team members and assign appropriate roles
4. **Customize Settings**: Configure permit types, PPE requirements, and safety procedures
5. **Test Workflows**: Create test permits and verify the approval process

## 🔄 Updates & Maintenance

### Keeping Up to Date
```bash
# Pull latest changes
git pull origin main

# Update dependencies
npm run install:all

# Run migrations
cd backend && npm run migrate

# Rebuild shared package
npm run build:shared
```

### Backup Strategy
- Regular database backups
- Environment configuration backups
- Application data backups

---

For additional help, refer to the main [README.md](README.md) or contact the development team.
