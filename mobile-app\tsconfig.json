{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": true, "isolatedModules": true}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}