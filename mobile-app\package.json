{"name": "@epermit/mobile-app", "version": "1.0.0", "description": "e-Permit Mobile App for Field Workers", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "eas build", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit": "eas submit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@epermit/shared": "^1.0.0", "expo": "~49.0.0", "expo-status-bar": "~1.6.0", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-font": "~11.4.0", "expo-splash-screen": "~0.20.5", "expo-system-ui": "~2.4.0", "expo-web-browser": "~12.3.2", "expo-camera": "~13.4.2", "expo-barcode-scanner": "~12.5.3", "expo-av": "~13.4.1", "expo-file-system": "~15.4.3", "expo-sqlite": "~11.3.3", "expo-network": "~5.4.0", "expo-notifications": "~0.20.1", "expo-location": "~16.1.0", "react": "18.2.0", "react-native": "0.72.3", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-vector-icons": "^10.0.0", "@expo/vector-icons": "^13.0.0", "react-native-paper": "^5.10.1", "react-native-elements": "^3.4.3", "firebase": "^10.1.0", "axios": "^1.4.0", "@react-native-async-storage/async-storage": "1.18.2", "react-native-voice": "^3.2.4", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.2.0", "react-native-svg": "13.9.0", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.6", "react-native-date-picker": "^4.2.13", "react-native-picker-select": "^8.0.4", "react-hook-form": "^7.45.2", "zustand": "^4.4.1", "react-query": "^3.39.3", "date-fns": "^2.30.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-expo": "^7.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.6.1", "jest-expo": "~49.0.0", "@testing-library/react-native": "^12.1.3", "@testing-library/jest-native": "^5.4.2", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true}