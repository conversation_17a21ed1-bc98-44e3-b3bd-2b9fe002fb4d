import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import NetInfo from '@react-native-community/netinfo';
import { syncService } from '../services/sync';

interface NetworkContextType {
  isConnected: boolean;
  isInternetReachable: boolean;
  connectionType: string | null;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  syncNow: () => Promise<void>;
}

const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

export const useNetwork = (): NetworkContextType => {
  const context = useContext(NetworkContext);
  if (!context) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
};

interface NetworkProviderProps {
  children: ReactNode;
}

export const NetworkProvider: React.FC<NetworkProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isInternetReachable, setIsInternetReachable] = useState(false);
  const [connectionType, setConnectionType] = useState<string | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  useEffect(() => {
    // Subscribe to network state updates
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected ?? false);
      setIsInternetReachable(state.isInternetReachable ?? false);
      setConnectionType(state.type);

      // Auto-sync when connection is restored
      if (state.isConnected && state.isInternetReachable && !isSyncing) {
        autoSync();
      }
    });

    // Get initial network state
    NetInfo.fetch().then(state => {
      setIsConnected(state.isConnected ?? false);
      setIsInternetReachable(state.isInternetReachable ?? false);
      setConnectionType(state.type);
    });

    return unsubscribe;
  }, []);

  const autoSync = async () => {
    try {
      setIsSyncing(true);
      await syncService.syncAll();
      setLastSyncTime(new Date());
    } catch (error) {
      console.error('Auto-sync failed:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const syncNow = async (): Promise<void> => {
    if (!isConnected || !isInternetReachable) {
      throw new Error('No internet connection available');
    }

    try {
      setIsSyncing(true);
      await syncService.syncAll();
      setLastSyncTime(new Date());
    } catch (error) {
      console.error('Manual sync failed:', error);
      throw error;
    } finally {
      setIsSyncing(false);
    }
  };

  const value: NetworkContextType = {
    isConnected,
    isInternetReachable,
    connectionType,
    isSyncing,
    lastSyncTime,
    syncNow,
  };

  return (
    <NetworkContext.Provider value={value}>
      {children}
    </NetworkContext.Provider>
  );
};
