import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { Helmet } from 'react-helmet-async';

import { useAuth } from './contexts/AuthContext';
import Layout from './components/Layout';
import LoadingScreen from './components/LoadingScreen';

// Auth pages
import LoginPage from './pages/auth/LoginPage';

// Dashboard pages
import DashboardPage from './pages/DashboardPage';
import PermitsPage from './pages/permits/PermitsPage';
import PermitDetailPage from './pages/permits/PermitDetailPage';
import CreatePermitPage from './pages/permits/CreatePermitPage';
import AnalyticsPage from './pages/AnalyticsPage';
import UsersPage from './pages/users/UsersPage';
import SitesPage from './pages/sites/SitesPage';
import SettingsPage from './pages/SettingsPage';
import ProfilePage from './pages/ProfilePage';

// Error pages
import NotFoundPage from './pages/errors/NotFoundPage';

function App() {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <>
      <Helmet>
        <title>e-Permit Admin Dashboard</title>
        <meta name="description" content="Smart e-Permit Platform Admin Dashboard for Industrial Safety Management" />
      </Helmet>

      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {user ? (
          <Layout>
            <Routes>
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="/dashboard" element={<DashboardPage />} />
              
              {/* Permits */}
              <Route path="/permits" element={<PermitsPage />} />
              <Route path="/permits/create" element={<CreatePermitPage />} />
              <Route path="/permits/:id" element={<PermitDetailPage />} />
              
              {/* Analytics */}
              <Route path="/analytics" element={<AnalyticsPage />} />
              
              {/* Users (Admin only) */}
              <Route path="/users" element={<UsersPage />} />
              
              {/* Sites (Admin only) */}
              <Route path="/sites" element={<SitesPage />} />
              
              {/* Settings & Profile */}
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/profile" element={<ProfilePage />} />
              
              {/* 404 */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Layout>
        ) : (
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        )}
      </Box>
    </>
  );
}

export default App;
