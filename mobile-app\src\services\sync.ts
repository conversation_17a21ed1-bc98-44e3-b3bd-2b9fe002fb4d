import { apiService } from './api';
import {
  getAllRecords,
  getUnsyncedRecords,
  markAsSynced,
  insertRecord,
  updateRecord,
  deleteRecord,
  executeSql,
} from './database';
import { Permit, User, Site } from '@epermit/shared';

class SyncService {
  private isSyncing = false;

  async syncAll(): Promise<void> {
    if (this.isSyncing) {
      console.log('Sync already in progress');
      return;
    }

    this.isSyncing = true;
    
    try {
      console.log('Starting sync...');
      
      // Sync in order: users, sites, permits
      await this.syncUsers();
      await this.syncSites();
      await this.syncPermits();
      await this.syncVoiceNotes();
      await this.syncAttachments();
      
      console.log('Sync completed successfully');
    } catch (error) {
      console.error('Sync failed:', error);
      throw error;
    } finally {
      this.isSyncing = false;
    }
  }

  private async syncUsers(): Promise<void> {
    try {
      // Upload unsynced local users (if any)
      const unsyncedUsers = await getUnsyncedRecords('users');
      for (const user of unsyncedUsers) {
        try {
          // In most cases, users are managed by admin, so we just mark as synced
          await markAsSynced('users', user.id);
        } catch (error) {
          console.error(`Failed to sync user ${user.id}:`, error);
        }
      }
    } catch (error) {
      console.error('User sync failed:', error);
    }
  }

  private async syncSites(): Promise<void> {
    try {
      // Download sites from server (read-only for mobile users)
      // Sites are typically managed by admins, so we just ensure we have the latest data
      
      const unsyncedSites = await getUnsyncedRecords('sites');
      for (const site of unsyncedSites) {
        try {
          await markAsSynced('sites', site.id);
        } catch (error) {
          console.error(`Failed to sync site ${site.id}:`, error);
        }
      }
    } catch (error) {
      console.error('Site sync failed:', error);
    }
  }

  private async syncPermits(): Promise<void> {
    try {
      // Upload unsynced local permits
      const unsyncedPermits = await getUnsyncedRecords('permits');
      
      for (const localPermit of unsyncedPermits) {
        try {
          const permitData = this.convertLocalPermitToApi(localPermit);
          
          if (localPermit.created_locally) {
            // Create new permit on server
            const serverPermit = await apiService.createPermit(permitData);
            
            // Update local record with server ID and mark as synced
            await updateRecord('permits', localPermit.id, {
              id: serverPermit.id,
              permit_number: serverPermit.permitNumber,
              synced: 1,
              created_locally: 0,
            });
          } else {
            // Update existing permit on server
            await apiService.updatePermit(localPermit.id, permitData);
            await markAsSynced('permits', localPermit.id);
          }
        } catch (error) {
          console.error(`Failed to sync permit ${localPermit.id}:`, error);
          // Add to sync queue for retry
          await this.addToSyncQueue('permits', localPermit.id, 'UPDATE', localPermit);
        }
      }

      // Download recent permits from server
      await this.downloadRecentPermits();
    } catch (error) {
      console.error('Permit sync failed:', error);
    }
  }

  private async downloadRecentPermits(): Promise<void> {
    try {
      // Get permits from last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const response = await apiService.getPermits({
        startDate: thirtyDaysAgo.toISOString(),
        limit: 100,
      });

      for (const serverPermit of response.permits) {
        try {
          const localPermit = await this.getLocalPermitById(serverPermit.id);
          const permitData = this.convertApiPermitToLocal(serverPermit);

          if (localPermit) {
            // Update existing local permit
            await updateRecord('permits', serverPermit.id, permitData);
          } else {
            // Insert new permit
            await insertRecord('permits', permitData);
          }
        } catch (error) {
          console.error(`Failed to save permit ${serverPermit.id} locally:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to download recent permits:', error);
    }
  }

  private async syncVoiceNotes(): Promise<void> {
    try {
      const unsyncedVoiceNotes = await getUnsyncedRecords('voice_notes');
      
      for (const voiceNote of unsyncedVoiceNotes) {
        try {
          // Upload voice note file
          const formData = new FormData();
          formData.append('audio', {
            uri: voiceNote.file_path,
            type: 'audio/m4a',
            name: `voice_note_${voiceNote.id}.m4a`,
          } as any);
          formData.append('duration', voiceNote.duration.toString());

          await apiService.uploadVoiceNote(voiceNote.permit_id, formData);
          await markAsSynced('voice_notes', voiceNote.id);
        } catch (error) {
          console.error(`Failed to sync voice note ${voiceNote.id}:`, error);
          await this.addToSyncQueue('voice_notes', voiceNote.id, 'UPLOAD', voiceNote);
        }
      }
    } catch (error) {
      console.error('Voice notes sync failed:', error);
    }
  }

  private async syncAttachments(): Promise<void> {
    try {
      const unsyncedAttachments = await getUnsyncedRecords('attachments');
      
      for (const attachment of unsyncedAttachments) {
        try {
          // Upload attachment file
          const formData = new FormData();
          formData.append('file', {
            uri: attachment.file_path,
            type: attachment.file_type,
            name: attachment.file_name,
          } as any);

          await apiService.uploadFile(formData);
          await markAsSynced('attachments', attachment.id);
        } catch (error) {
          console.error(`Failed to sync attachment ${attachment.id}:`, error);
          await this.addToSyncQueue('attachments', attachment.id, 'UPLOAD', attachment);
        }
      }
    } catch (error) {
      console.error('Attachments sync failed:', error);
    }
  }

  private convertLocalPermitToApi(localPermit: any): Partial<Permit> {
    return {
      title: localPermit.title,
      description: localPermit.description,
      type: localPermit.type,
      status: localPermit.status,
      location: localPermit.location,
      siteId: localPermit.site_id,
      supervisorId: localPermit.supervisor_id,
      startDate: new Date(localPermit.start_date),
      endDate: new Date(localPermit.end_date),
      emergencyContact: localPermit.emergency_contact,
      isolationRequired: Boolean(localPermit.isolation_required),
      isolationSteps: localPermit.isolation_steps ? JSON.parse(localPermit.isolation_steps) : [],
      ppeRequired: JSON.parse(localPermit.ppe_required),
      safetySteps: JSON.parse(localPermit.safety_steps),
    };
  }

  private convertApiPermitToLocal(serverPermit: Permit): any {
    return {
      id: serverPermit.id,
      permit_number: serverPermit.permitNumber,
      title: serverPermit.title,
      description: serverPermit.description,
      type: serverPermit.type,
      status: serverPermit.status,
      risk_level: serverPermit.riskLevel,
      location: serverPermit.location,
      site_id: serverPermit.siteId,
      requested_by: serverPermit.requestedBy,
      approved_by: serverPermit.approvedBy,
      supervisor_id: serverPermit.supervisorId,
      start_date: serverPermit.startDate.toISOString(),
      end_date: serverPermit.endDate.toISOString(),
      actual_start_date: serverPermit.actualStartDate?.toISOString(),
      actual_end_date: serverPermit.actualEndDate?.toISOString(),
      ppe_required: JSON.stringify(serverPermit.ppeRequired),
      safety_steps: JSON.stringify(serverPermit.safetySteps),
      isolation_required: serverPermit.isolationRequired ? 1 : 0,
      isolation_steps: serverPermit.isolationSteps ? JSON.stringify(serverPermit.isolationSteps) : null,
      emergency_contact: serverPermit.emergencyContact,
      qr_code: serverPermit.qrCode,
      voice_notes: serverPermit.voiceNotes ? JSON.stringify(serverPermit.voiceNotes) : null,
      attachments: serverPermit.attachments ? JSON.stringify(serverPermit.attachments) : null,
      ai_recommendations: serverPermit.aiRecommendations ? JSON.stringify(serverPermit.aiRecommendations) : null,
      approval_notes: serverPermit.approvalNotes,
      rejection_reason: serverPermit.rejectionReason,
      created_at: serverPermit.createdAt.toISOString(),
      updated_at: serverPermit.updatedAt.toISOString(),
      synced: 1,
    };
  }

  private async getLocalPermitById(id: string): Promise<any | null> {
    try {
      const result = await executeSql('SELECT * FROM permits WHERE id = ?', [id]);
      return result.rows.length > 0 ? result.rows._array[0] : null;
    } catch (error) {
      console.error('Error getting local permit:', error);
      return null;
    }
  }

  private async addToSyncQueue(
    tableName: string,
    recordId: string,
    operation: string,
    data: any
  ): Promise<void> {
    try {
      await insertRecord('sync_queue', {
        table_name: tableName,
        record_id: recordId,
        operation,
        data: JSON.stringify(data),
        created_at: new Date().toISOString(),
        attempts: 0,
      });
    } catch (error) {
      console.error('Failed to add to sync queue:', error);
    }
  }

  async retrySyncQueue(): Promise<void> {
    try {
      const queueItems = await executeSql(
        'SELECT * FROM sync_queue WHERE attempts < 3 ORDER BY created_at ASC LIMIT 10'
      );

      for (const item of queueItems.rows._array) {
        try {
          const data = JSON.parse(item.data);
          
          switch (item.operation) {
            case 'UPDATE':
              if (item.table_name === 'permits') {
                await apiService.updatePermit(item.record_id, this.convertLocalPermitToApi(data));
                await markAsSynced('permits', item.record_id);
              }
              break;
            case 'UPLOAD':
              // Handle file uploads
              break;
          }

          // Remove from queue on success
          await executeSql('DELETE FROM sync_queue WHERE id = ?', [item.id]);
        } catch (error) {
          // Increment attempts
          await executeSql(
            'UPDATE sync_queue SET attempts = attempts + 1, last_attempt_at = ?, error_message = ? WHERE id = ?',
            [new Date().toISOString(), error.message, item.id]
          );
        }
      }
    } catch (error) {
      console.error('Retry sync queue failed:', error);
    }
  }
}

export const syncService = new SyncService();
