import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/database';

interface SiteAttributes {
  id: string;
  name: string;
  location: string;
  timezone: string;
  contactEmail: string;
  contactPhone: string;
  isActive: boolean;
  settings: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

interface SiteCreationAttributes extends Optional<SiteAttributes, 'id' | 'createdAt' | 'updatedAt' | 'settings'> {}

class Site extends Model<SiteAttributes, SiteCreationAttributes> implements SiteAttributes {
  public id!: string;
  public name!: string;
  public location!: string;
  public timezone!: string;
  public contactEmail!: string;
  public contactPhone!: string;
  public isActive!: boolean;
  public settings!: Record<string, any>;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Site.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    location: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 200],
      },
    },
    timezone: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'UTC',
    },
    contactEmail: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    contactPhone: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [10, 20],
      },
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    settings: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: 'Site',
    tableName: 'sites',
    indexes: [
      {
        fields: ['name'],
      },
      {
        fields: ['isActive'],
      },
    ],
  }
);

export default Site;
