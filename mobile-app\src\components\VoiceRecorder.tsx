import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Alert,
} from 'react-native';
import {
  Text,
  Button,
  Surface,
  IconButton,
  ProgressBar,
} from 'react-native-paper';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing } from '../theme';

interface VoiceRecorderProps {
  visible: boolean;
  onClose: () => void;
  onSave: (audioPath: string) => void;
}

export default function VoiceRecorder({ visible, onClose, onSave }: VoiceRecorderProps) {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingUri, setRecordingUri] = useState<string | null>(null);

  useEffect(() => {
    return () => {
      if (recording) {
        recording.stopAndUnloadAsync();
      }
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [recording, sound]);

  const startRecording = async () => {
    try {
      const permission = await Audio.requestPermissionsAsync();
      if (permission.status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant microphone permission to record voice notes.');
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(recording);
      setIsRecording(true);
      setRecordingDuration(0);

      // Update duration every second
      const interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      recording.setOnRecordingStatusUpdate((status) => {
        if (!status.isRecording) {
          clearInterval(interval);
        }
      });

    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      setIsRecording(false);
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      setRecordingUri(uri);
      setRecording(null);
    } catch (error) {
      console.error('Failed to stop recording:', error);
      Alert.alert('Error', 'Failed to stop recording.');
    }
  };

  const playRecording = async () => {
    if (!recordingUri) return;

    try {
      if (sound) {
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: recordingUri },
        { shouldPlay: true }
      );

      setSound(newSound);
      setIsPlaying(true);

      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded && status.didJustFinish) {
          setIsPlaying(false);
        }
      });
    } catch (error) {
      console.error('Failed to play recording:', error);
      Alert.alert('Error', 'Failed to play recording.');
    }
  };

  const stopPlayback = async () => {
    if (sound) {
      await sound.stopAsync();
      setIsPlaying(false);
    }
  };

  const saveRecording = async () => {
    if (!recordingUri) return;

    try {
      // Create a unique filename
      const timestamp = Date.now();
      const filename = `voice_note_${timestamp}.m4a`;
      const documentDirectory = FileSystem.documentDirectory;
      const newUri = `${documentDirectory}${filename}`;

      // Copy the recording to a permanent location
      await FileSystem.copyAsync({
        from: recordingUri,
        to: newUri,
      });

      onSave(newUri);
    } catch (error) {
      console.error('Failed to save recording:', error);
      Alert.alert('Error', 'Failed to save recording.');
    }
  };

  const discardRecording = () => {
    setRecordingUri(null);
    setRecordingDuration(0);
    setIsPlaying(false);
    if (sound) {
      sound.unloadAsync();
      setSound(null);
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleClose = () => {
    if (isRecording) {
      stopRecording();
    }
    if (isPlaying) {
      stopPlayback();
    }
    discardRecording();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Voice Note</Text>
          <IconButton
            icon="close"
            onPress={handleClose}
          />
        </View>

        <Surface style={styles.recorderContainer}>
          {!recordingUri ? (
            // Recording interface
            <View style={styles.recordingInterface}>
              <View style={styles.microphoneContainer}>
                <View style={[
                  styles.microphoneButton,
                  isRecording && styles.microphoneButtonActive
                ]}>
                  <Ionicons
                    name="mic"
                    size={48}
                    color={isRecording ? colors.white : colors.primary}
                  />
                </View>
                {isRecording && (
                  <View style={styles.recordingIndicator}>
                    <View style={styles.recordingDot} />
                    <Text style={styles.recordingText}>Recording...</Text>
                  </View>
                )}
              </View>

              <Text style={styles.duration}>
                {formatDuration(recordingDuration)}
              </Text>

              {isRecording && (
                <ProgressBar
                  progress={recordingDuration / 300} // Max 5 minutes
                  color={colors.error}
                  style={styles.progressBar}
                />
              )}

              <View style={styles.recordingControls}>
                {!isRecording ? (
                  <Button
                    mode="contained"
                    onPress={startRecording}
                    icon="microphone"
                    style={styles.recordButton}
                  >
                    Start Recording
                  </Button>
                ) : (
                  <Button
                    mode="contained"
                    onPress={stopRecording}
                    icon="stop"
                    buttonColor={colors.error}
                    style={styles.stopButton}
                  >
                    Stop Recording
                  </Button>
                )}
              </View>
            </View>
          ) : (
            // Playback interface
            <View style={styles.playbackInterface}>
              <View style={styles.playbackInfo}>
                <Ionicons name="musical-notes" size={48} color={colors.primary} />
                <Text style={styles.playbackDuration}>
                  Duration: {formatDuration(recordingDuration)}
                </Text>
              </View>

              <View style={styles.playbackControls}>
                <IconButton
                  icon={isPlaying ? "pause" : "play"}
                  size={32}
                  iconColor={colors.primary}
                  onPress={isPlaying ? stopPlayback : playRecording}
                  style={styles.playButton}
                />
              </View>

              <View style={styles.actionButtons}>
                <Button
                  mode="outlined"
                  onPress={discardRecording}
                  icon="delete"
                  style={styles.discardButton}
                >
                  Discard
                </Button>
                <Button
                  mode="contained"
                  onPress={saveRecording}
                  icon="check"
                  style={styles.saveButton}
                >
                  Save
                </Button>
              </View>
            </View>
          )}
        </Surface>

        <View style={styles.instructions}>
          <Text style={styles.instructionText}>
            {!recordingUri
              ? "Tap the microphone to start recording your voice note. Maximum duration is 5 minutes."
              : "Review your recording and save it to add to the permit."
            }
          </Text>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
  },
  recorderContainer: {
    margin: spacing.lg,
    padding: spacing.xl,
    borderRadius: 12,
    alignItems: 'center',
  },
  recordingInterface: {
    alignItems: 'center',
    width: '100%',
  },
  microphoneContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  microphoneButton: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.blue[50],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.primary,
  },
  microphoneButtonActive: {
    backgroundColor: colors.error,
    borderColor: colors.error,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.error,
    marginRight: spacing.xs,
  },
  recordingText: {
    fontSize: 14,
    color: colors.error,
    fontWeight: '500',
  },
  duration: {
    fontSize: 32,
    fontWeight: '700',
    color: colors.text,
    marginBottom: spacing.lg,
  },
  progressBar: {
    width: '100%',
    marginBottom: spacing.lg,
  },
  recordingControls: {
    width: '100%',
  },
  recordButton: {
    borderRadius: 8,
  },
  stopButton: {
    borderRadius: 8,
  },
  playbackInterface: {
    alignItems: 'center',
    width: '100%',
  },
  playbackInfo: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  playbackDuration: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  playbackControls: {
    marginBottom: spacing.xl,
  },
  playButton: {
    backgroundColor: colors.blue[50],
  },
  actionButtons: {
    flexDirection: 'row',
    gap: spacing.md,
    width: '100%',
  },
  discardButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
  instructions: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  instructionText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});
