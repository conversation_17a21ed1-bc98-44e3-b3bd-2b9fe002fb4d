import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/database';
import { PermitStatus, PermitType, RiskLevel, PPEItem, SafetyStep } from '@epermit/shared';

interface PermitAttributes {
  id: string;
  permitNumber: string;
  title: string;
  description: string;
  type: PermitType;
  status: PermitStatus;
  riskLevel: RiskLevel;
  location: string;
  siteId: string;
  requestedBy: string;
  approvedBy?: string;
  supervisorId?: string;
  startDate: Date;
  endDate: Date;
  actualStartDate?: Date;
  actualEndDate?: Date;
  ppeRequired: PPEItem[];
  safetySteps: SafetyStep[];
  isolationRequired: boolean;
  isolationSteps?: string[];
  emergencyContact: string;
  qrCode?: string;
  voiceNotes?: string[];
  attachments?: string[];
  aiRecommendations?: string[];
  approvalNotes?: string;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface PermitCreationAttributes extends Optional<PermitAttributes, 'id' | 'createdAt' | 'updatedAt' | 'approvedBy' | 'supervisorId' | 'actualStartDate' | 'actualEndDate' | 'isolationSteps' | 'qrCode' | 'voiceNotes' | 'attachments' | 'aiRecommendations' | 'approvalNotes' | 'rejectionReason'> {}

class Permit extends Model<PermitAttributes, PermitCreationAttributes> implements PermitAttributes {
  public id!: string;
  public permitNumber!: string;
  public title!: string;
  public description!: string;
  public type!: PermitType;
  public status!: PermitStatus;
  public riskLevel!: RiskLevel;
  public location!: string;
  public siteId!: string;
  public requestedBy!: string;
  public approvedBy?: string;
  public supervisorId?: string;
  public startDate!: Date;
  public endDate!: Date;
  public actualStartDate?: Date;
  public actualEndDate?: Date;
  public ppeRequired!: PPEItem[];
  public safetySteps!: SafetyStep[];
  public isolationRequired!: boolean;
  public isolationSteps?: string[];
  public emergencyContact!: string;
  public qrCode?: string;
  public voiceNotes?: string[];
  public attachments?: string[];
  public aiRecommendations?: string[];
  public approvalNotes?: string;
  public rejectionReason?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Permit.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    permitNumber: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [5, 200],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [10, 2000],
      },
    },
    type: {
      type: DataTypes.ENUM('HOT_WORK', 'CONFINED_SPACE', 'ELECTRICAL', 'HEIGHT_WORK', 'EXCAVATION', 'CHEMICAL_HANDLING', 'MAINTENANCE', 'OTHER'),
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM('DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED', 'EXPIRED'),
      allowNull: false,
      defaultValue: 'DRAFT',
    },
    riskLevel: {
      type: DataTypes.ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL'),
      allowNull: false,
    },
    location: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 200],
      },
    },
    siteId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'sites',
        key: 'id',
      },
    },
    requestedBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    approvedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    supervisorId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    actualStartDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    actualEndDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    ppeRequired: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: [],
    },
    safetySteps: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: [],
    },
    isolationRequired: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isolationSteps: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    emergencyContact: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [10, 20],
      },
    },
    qrCode: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    voiceNotes: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
    },
    attachments: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
    },
    aiRecommendations: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
    },
    approvalNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    rejectionReason: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: 'Permit',
    tableName: 'permits',
    indexes: [
      {
        fields: ['permitNumber'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['type'],
      },
      {
        fields: ['riskLevel'],
      },
      {
        fields: ['siteId'],
      },
      {
        fields: ['requestedBy'],
      },
      {
        fields: ['startDate', 'endDate'],
      },
    ],
  }
);

export default Permit;
