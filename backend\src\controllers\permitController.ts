import { Response } from 'express';
import { Op } from 'sequelize';
import { Permit, User, Site, AIRecommendation } from '../models';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { createAuditLog } from '../middleware/audit';
import { generatePermitNumber, calculateRiskLevel, getDefaultPPE, getDefaultSafetySteps, generateQRCodeData } from '@epermit/shared';
import QRCode from 'qrcode';

export const createPermit = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    title,
    description,
    type,
    location,
    siteId,
    startDate,
    endDate,
    emergencyContact,
    isolationRequired,
    isolationSteps,
    supervisorId,
  } = req.body;

  // Validate site access
  if (req.user.role !== 'ADMIN' && req.user.siteId !== siteId) {
    throw createError('Access denied to this site', 403);
  }

  // Generate permit number
  const permitNumber = generatePermitNumber(type, siteId);

  // Get default PPE and safety steps
  const ppeRequired = getDefaultPPE(type);
  const safetySteps = getDefaultSafetySteps(type);

  // Calculate risk level
  const riskLevel = calculateRiskLevel(type, location, isolationRequired, ppeRequired.length);

  // Generate QR code
  const qrData = generateQRCodeData('temp-id', permitNumber);
  const qrCode = await QRCode.toDataURL(qrData);

  const permit = await Permit.create({
    permitNumber,
    title,
    description,
    type,
    riskLevel,
    location,
    siteId,
    requestedBy: req.user.id,
    supervisorId,
    startDate: new Date(startDate),
    endDate: new Date(endDate),
    emergencyContact,
    isolationRequired,
    isolationSteps,
    ppeRequired,
    safetySteps,
    qrCode,
  });

  // Update QR code with actual permit ID
  const updatedQrData = generateQRCodeData(permit.id, permitNumber);
  const updatedQrCode = await QRCode.toDataURL(updatedQrData);
  await permit.update({ qrCode: updatedQrCode });

  await createAuditLog(
    req.user.id,
    'Permit',
    permit.id,
    'CREATE_PERMIT',
    {
      created: permit.toJSON(),
    },
    req.ip,
    req.get('User-Agent')
  );

  const permitWithDetails = await Permit.findByPk(permit.id, {
    include: [
      { model: User, as: 'requester', attributes: ['id', 'name', 'email'] },
      { model: User, as: 'supervisor', attributes: ['id', 'name', 'email'] },
      { model: Site, as: 'site', attributes: ['id', 'name', 'location'] },
    ],
  });

  res.status(201).json({
    success: true,
    data: permitWithDetails,
    message: 'Permit created successfully',
  });
});

export const getPermits = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    page = 1,
    limit = 10,
    status,
    type,
    riskLevel,
    siteId,
    requestedBy,
    startDate,
    endDate,
    sortBy = 'createdAt',
    sortOrder = 'DESC',
  } = req.query;

  const offset = (Number(page) - 1) * Number(limit);

  // Build where clause
  const where: any = {};

  // Site access control
  if (req.user.role !== 'ADMIN') {
    where.siteId = req.user.siteId;
  } else if (siteId) {
    where.siteId = siteId;
  }

  if (status) where.status = status;
  if (type) where.type = type;
  if (riskLevel) where.riskLevel = riskLevel;
  if (requestedBy) where.requestedBy = requestedBy;

  if (startDate || endDate) {
    where.startDate = {};
    if (startDate) where.startDate[Op.gte] = new Date(startDate as string);
    if (endDate) where.startDate[Op.lte] = new Date(endDate as string);
  }

  const { count, rows: permits } = await Permit.findAndCountAll({
    where,
    include: [
      { model: User, as: 'requester', attributes: ['id', 'name', 'email'] },
      { model: User, as: 'approver', attributes: ['id', 'name', 'email'] },
      { model: User, as: 'supervisor', attributes: ['id', 'name', 'email'] },
      { model: Site, as: 'site', attributes: ['id', 'name', 'location'] },
    ],
    order: [[sortBy as string, sortOrder as string]],
    limit: Number(limit),
    offset,
  });

  res.json({
    success: true,
    data: {
      permits,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count,
        pages: Math.ceil(count / Number(limit)),
      },
    },
  });
});

export const getPermitById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  const permit = await Permit.findByPk(id, {
    include: [
      { model: User, as: 'requester', attributes: ['id', 'name', 'email', 'role'] },
      { model: User, as: 'approver', attributes: ['id', 'name', 'email', 'role'] },
      { model: User, as: 'supervisor', attributes: ['id', 'name', 'email', 'role'] },
      { model: Site, as: 'site', attributes: ['id', 'name', 'location', 'timezone'] },
      { model: AIRecommendation, as: 'recommendations' },
    ],
  });

  if (!permit) {
    throw createError('Permit not found', 404);
  }

  // Check site access
  if (req.user.role !== 'ADMIN' && req.user.siteId !== permit.siteId) {
    throw createError('Access denied to this permit', 403);
  }

  res.json({
    success: true,
    data: permit,
  });
});

export const updatePermit = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;
  const updates = req.body;

  const permit = await Permit.findByPk(id);
  if (!permit) {
    throw createError('Permit not found', 404);
  }

  // Check permissions
  const canEdit = 
    req.user.role === 'ADMIN' ||
    permit.requestedBy === req.user.id ||
    (req.user.role === 'SUPERVISOR' && permit.supervisorId === req.user.id);

  if (!canEdit) {
    throw createError('Permission denied to edit this permit', 403);
  }

  // Prevent editing approved/active permits unless admin
  if (['APPROVED', 'ACTIVE', 'COMPLETED'].includes(permit.status) && req.user.role !== 'ADMIN') {
    throw createError('Cannot edit permit in current status', 400);
  }

  const originalData = permit.toJSON();

  // Recalculate risk level if relevant fields changed
  if (updates.type || updates.location || updates.isolationRequired || updates.ppeRequired) {
    updates.riskLevel = calculateRiskLevel(
      updates.type || permit.type,
      updates.location || permit.location,
      updates.isolationRequired !== undefined ? updates.isolationRequired : permit.isolationRequired,
      updates.ppeRequired ? updates.ppeRequired.length : permit.ppeRequired.length
    );
  }

  await permit.update(updates);

  await createAuditLog(
    req.user.id,
    'Permit',
    permit.id,
    'UPDATE_PERMIT',
    {
      before: originalData,
      after: permit.toJSON(),
      changes: updates,
    },
    req.ip,
    req.get('User-Agent')
  );

  const updatedPermit = await Permit.findByPk(permit.id, {
    include: [
      { model: User, as: 'requester', attributes: ['id', 'name', 'email'] },
      { model: User, as: 'approver', attributes: ['id', 'name', 'email'] },
      { model: User, as: 'supervisor', attributes: ['id', 'name', 'email'] },
      { model: Site, as: 'site', attributes: ['id', 'name', 'location'] },
    ],
  });

  res.json({
    success: true,
    data: updatedPermit,
    message: 'Permit updated successfully',
  });
});

export const approvePermit = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;
  const { approved, notes, rejectionReason } = req.body;

  // Check approval permissions
  if (!['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'].includes(req.user.role)) {
    throw createError('Permission denied to approve permits', 403);
  }

  const permit = await Permit.findByPk(id);
  if (!permit) {
    throw createError('Permit not found', 404);
  }

  if (permit.status !== 'PENDING_APPROVAL') {
    throw createError('Permit is not pending approval', 400);
  }

  // Check site access
  if (req.user.role !== 'ADMIN' && req.user.siteId !== permit.siteId) {
    throw createError('Access denied to this permit', 403);
  }

  const originalData = permit.toJSON();

  const updates: any = {
    approvedBy: req.user.id,
    status: approved ? 'APPROVED' : 'CANCELLED',
    approvalNotes: notes,
  };

  if (!approved && rejectionReason) {
    updates.rejectionReason = rejectionReason;
  }

  await permit.update(updates);

  await createAuditLog(
    req.user.id,
    'Permit',
    permit.id,
    approved ? 'APPROVE_PERMIT' : 'REJECT_PERMIT',
    {
      before: originalData,
      after: permit.toJSON(),
      approved,
      notes,
      rejectionReason,
    },
    req.ip,
    req.get('User-Agent')
  );

  res.json({
    success: true,
    data: permit,
    message: `Permit ${approved ? 'approved' : 'rejected'} successfully`,
  });
});

export const submitForApproval = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  const permit = await Permit.findByPk(id);
  if (!permit) {
    throw createError('Permit not found', 404);
  }

  if (permit.requestedBy !== req.user.id && req.user.role !== 'ADMIN') {
    throw createError('Permission denied', 403);
  }

  if (permit.status !== 'DRAFT') {
    throw createError('Only draft permits can be submitted for approval', 400);
  }

  await permit.update({ status: 'PENDING_APPROVAL' });

  await createAuditLog(
    req.user.id,
    'Permit',
    permit.id,
    'SUBMIT_FOR_APPROVAL',
    {
      statusChange: { from: 'DRAFT', to: 'PENDING_APPROVAL' },
    },
    req.ip,
    req.get('User-Agent')
  );

  res.json({
    success: true,
    data: permit,
    message: 'Permit submitted for approval',
  });
});

export const deletePermit = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  const permit = await Permit.findByPk(id);
  if (!permit) {
    throw createError('Permit not found', 404);
  }

  // Only allow deletion of draft permits or by admin
  if (permit.status !== 'DRAFT' && req.user.role !== 'ADMIN') {
    throw createError('Only draft permits can be deleted', 400);
  }

  // Check permissions
  const canDelete = 
    req.user.role === 'ADMIN' ||
    permit.requestedBy === req.user.id;

  if (!canDelete) {
    throw createError('Permission denied to delete this permit', 403);
  }

  const permitData = permit.toJSON();
  await permit.destroy();

  await createAuditLog(
    req.user.id,
    'Permit',
    permit.id,
    'DELETE_PERMIT',
    {
      deleted: permitData,
    },
    req.ip,
    req.get('User-Agent')
  );

  res.json({
    success: true,
    message: 'Permit deleted successfully',
  });
});
