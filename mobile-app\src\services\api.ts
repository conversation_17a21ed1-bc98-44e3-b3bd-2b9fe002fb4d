import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Permit, User, ApiResponse } from '@epermit/shared';

const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api/v1' 
  : 'https://your-api-domain.com/api/v1';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          await AsyncStorage.multiRemove(['authToken', 'userData']);
          // Redirect to login would be handled by AuthContext
        }
        
        const message = error.response?.data?.error || error.message || 'Network error';
        throw new Error(message);
      }
    );
  }

  // Auth endpoints
  async getCurrentUser(): Promise<User> {
    const response: ApiResponse<User> = await this.api.get('/auth/me');
    return response.data!;
  }

  async getUserPermissions(): Promise<any> {
    const response: ApiResponse = await this.api.get('/auth/permissions');
    return response.data;
  }

  // Permit endpoints
  async getPermits(params?: {
    page?: number;
    limit?: number;
    status?: string;
    type?: string;
    riskLevel?: string;
    siteId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{ permits: Permit[]; pagination: any }> {
    const response: ApiResponse = await this.api.get('/permits', { params });
    return response.data!;
  }

  async getPermitById(id: string): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.get(`/permits/${id}`);
    return response.data!;
  }

  async createPermit(permitData: Partial<Permit>): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.post('/permits', permitData);
    return response.data!;
  }

  async updatePermit(id: string, updates: Partial<Permit>): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.put(`/permits/${id}`, updates);
    return response.data!;
  }

  async submitPermitForApproval(id: string): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.post(`/permits/${id}/submit`);
    return response.data!;
  }

  async deletePermit(id: string): Promise<void> {
    await this.api.delete(`/permits/${id}`);
  }

  // AI endpoints
  async generateAIRecommendations(permitId: string, permitData: any): Promise<any[]> {
    const response: ApiResponse = await this.api.post(
      `/ai/permits/${permitId}/recommendations`,
      permitData
    );
    return response.data!;
  }

  async getAIRecommendations(permitId: string, params?: {
    type?: string;
    applied?: boolean;
  }): Promise<any[]> {
    const response: ApiResponse = await this.api.get(
      `/ai/permits/${permitId}/recommendations`,
      { params }
    );
    return response.data!;
  }

  async applyAIRecommendation(recommendationId: string): Promise<any> {
    const response: ApiResponse = await this.api.post(
      `/ai/recommendations/${recommendationId}/apply`
    );
    return response.data!;
  }

  // Analytics endpoints
  async getPermitAnalytics(params?: {
    siteId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<any> {
    const response: ApiResponse = await this.api.get('/analytics/permits', { params });
    return response.data!;
  }

  async getKPIDashboard(params?: { siteId?: string }): Promise<any> {
    const response: ApiResponse = await this.api.get('/analytics/kpis', { params });
    return response.data!;
  }

  // File upload endpoints
  async uploadFile(file: FormData): Promise<{ url: string; filename: string }> {
    const response: ApiResponse = await this.api.post('/upload', file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data!;
  }

  // Voice note endpoints
  async uploadVoiceNote(permitId: string, audioData: FormData): Promise<any> {
    const response: ApiResponse = await this.api.post(
      `/permits/${permitId}/voice-notes`,
      audioData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data!;
  }

  // QR code verification
  async verifyQRCode(qrData: string): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.post('/permits/verify-qr', {
      qrData,
    });
    return response.data!;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.api.get('/health');
      return true;
    } catch (error) {
      return false;
    }
  }
}

export const apiService = new ApiService();
