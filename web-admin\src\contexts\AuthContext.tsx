import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { initializeApp } from 'firebase/app';
import {
  getAuth,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
} from 'firebase/auth';
import { User } from '@epermit/shared';
import { apiService } from '../services/api';

// Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setFirebaseUser(firebaseUser);
      
      if (firebaseUser) {
        try {
          // Get ID token
          const token = await firebaseUser.getIdToken();
          
          // Store token for API calls
          localStorage.setItem('authToken', token);
          
          // Get user profile from backend
          const userProfile = await apiService.getCurrentUser();
          setUser(userProfile);
          
          // Store user data locally
          localStorage.setItem('userData', JSON.stringify(userProfile));
        } catch (error) {
          console.error('Error fetching user profile:', error);
          // Try to load cached user data
          try {
            const cachedUserData = localStorage.getItem('userData');
            if (cachedUserData) {
              setUser(JSON.parse(cachedUserData));
            }
          } catch (cacheError) {
            console.error('Error loading cached user data:', cacheError);
          }
        }
      } else {
        setUser(null);
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string): Promise<void> => {
    try {
      setLoading(true);
      await signInWithEmailAndPassword(auth, email, password);
      // User state will be updated by onAuthStateChanged
    } catch (error: any) {
      setLoading(false);
      throw new Error(error.message || 'Sign in failed');
    }
  };

  const signOutUser = async (): Promise<void> => {
    try {
      setLoading(true);
      await signOut(auth);
      // User state will be updated by onAuthStateChanged
    } catch (error: any) {
      setLoading(false);
      throw new Error(error.message || 'Sign out failed');
    }
  };

  const refreshUser = async (): Promise<void> => {
    if (firebaseUser) {
      try {
        // Force token refresh
        const token = await firebaseUser.getIdToken(true);
        localStorage.setItem('authToken', token);
        
        // Fetch updated user profile
        const userProfile = await apiService.getCurrentUser();
        setUser(userProfile);
        
        // Update cached user data
        localStorage.setItem('userData', JSON.stringify(userProfile));
      } catch (error) {
        console.error('Error refreshing user:', error);
      }
    }
  };

  const value: AuthContextType = {
    user,
    firebaseUser,
    loading,
    signIn,
    signOut: signOutUser,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
