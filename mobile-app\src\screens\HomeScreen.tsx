import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  Avatar,
  Surface,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useNetwork } from '../contexts/NetworkContext';
import { colors, spacing, typography } from '../theme';
import { apiService } from '../services/api';
import { Permit } from '@epermit/shared';

export default function HomeScreen({ navigation }: any) {
  const { user } = useAuth();
  const { isConnected, isSyncing, lastSyncTime, syncNow } = useNetwork();
  const [refreshing, setRefreshing] = useState(false);
  const [recentPermits, setRecentPermits] = useState<Permit[]>([]);
  const [stats, setStats] = useState({
    activePermits: 0,
    pendingApprovals: 0,
    completedToday: 0,
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Load recent permits
      const permitsResponse = await apiService.getPermits({
        limit: 5,
        requestedBy: user?.id,
      });
      setRecentPermits(permitsResponse.permits);

      // Load stats
      const activeResponse = await apiService.getPermits({
        status: 'ACTIVE',
        requestedBy: user?.id,
      });
      const pendingResponse = await apiService.getPermits({
        status: 'PENDING_APPROVAL',
        requestedBy: user?.id,
      });
      const today = new Date().toISOString().split('T')[0];
      const completedResponse = await apiService.getPermits({
        status: 'COMPLETED',
        startDate: today,
        requestedBy: user?.id,
      });

      setStats({
        activePermits: activeResponse.permits.length,
        pendingApprovals: pendingResponse.permits.length,
        completedToday: completedResponse.permits.length,
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      if (isConnected) {
        await syncNow();
      }
      await loadDashboardData();
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return colors.success;
      case 'PENDING_APPROVAL': return colors.warning;
      case 'APPROVED': return colors.blue[500];
      case 'COMPLETED': return colors.success;
      case 'CANCELLED': return colors.error;
      case 'EXPIRED': return colors.error;
      default: return colors.gray[500];
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'LOW': return colors.success;
      case 'MEDIUM': return colors.warning;
      case 'HIGH': return colors.amber[600];
      case 'CRITICAL': return colors.error;
      default: return colors.gray[500];
    }
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Good morning,</Text>
          <Text style={styles.userName}>{user?.name}</Text>
        </View>
        <TouchableOpacity
          style={styles.qrButton}
          onPress={() => navigation.navigate('QRScanner')}
        >
          <Ionicons name="qr-code" size={24} color={colors.white} />
        </TouchableOpacity>
      </View>

      {/* Connection Status */}
      <Surface style={styles.connectionStatus}>
        <View style={styles.connectionInfo}>
          <Ionicons
            name={isConnected ? 'wifi' : 'wifi-off'}
            size={16}
            color={isConnected ? colors.success : colors.error}
          />
          <Text style={styles.connectionText}>
            {isConnected ? 'Online' : 'Offline'}
          </Text>
          {isSyncing && (
            <Text style={styles.syncingText}>• Syncing...</Text>
          )}
        </View>
        {lastSyncTime && (
          <Text style={styles.lastSyncText}>
            Last sync: {lastSyncTime.toLocaleTimeString()}
          </Text>
        )}
      </Surface>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <Card style={[styles.statCard, { backgroundColor: colors.blue[50] }]}>
          <Card.Content style={styles.statContent}>
            <Text style={styles.statNumber}>{stats.activePermits}</Text>
            <Text style={styles.statLabel}>Active Permits</Text>
          </Card.Content>
        </Card>
        <Card style={[styles.statCard, { backgroundColor: colors.amber[50] }]}>
          <Card.Content style={styles.statContent}>
            <Text style={styles.statNumber}>{stats.pendingApprovals}</Text>
            <Text style={styles.statLabel}>Pending Approval</Text>
          </Card.Content>
        </Card>
        <Card style={[styles.statCard, { backgroundColor: colors.green[50] }]}>
          <Card.Content style={styles.statContent}>
            <Text style={styles.statNumber}>{stats.completedToday}</Text>
            <Text style={styles.statLabel}>Completed Today</Text>
          </Card.Content>
        </Card>
      </View>

      {/* Quick Actions */}
      <Card style={styles.actionsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsContainer}>
            <Button
              mode="contained"
              icon="plus"
              onPress={() => navigation.navigate('Permits', { screen: 'CreatePermit' })}
              style={styles.actionButton}
            >
              New Permit
            </Button>
            <Button
              mode="outlined"
              icon="qr-code-scanner"
              onPress={() => navigation.navigate('QRScanner')}
              style={styles.actionButton}
            >
              Scan QR
            </Button>
          </View>
        </Card.Content>
      </Card>

      {/* Recent Permits */}
      <Card style={styles.permitsCard}>
        <Card.Content>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Permits</Text>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Permits')}
            >
              View All
            </Button>
          </View>
          
          {recentPermits.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="document-outline" size={48} color={colors.gray[400]} />
              <Text style={styles.emptyText}>No permits yet</Text>
              <Text style={styles.emptySubtext}>
                Create your first permit to get started
              </Text>
            </View>
          ) : (
            recentPermits.map((permit) => (
              <TouchableOpacity
                key={permit.id}
                style={styles.permitItem}
                onPress={() => navigation.navigate('Permits', {
                  screen: 'PermitDetail',
                  params: { permitId: permit.id }
                })}
              >
                <View style={styles.permitHeader}>
                  <Text style={styles.permitTitle}>{permit.title}</Text>
                  <Chip
                    mode="outlined"
                    textStyle={{ color: getStatusColor(permit.status) }}
                    style={{ borderColor: getStatusColor(permit.status) }}
                  >
                    {permit.status.replace('_', ' ')}
                  </Chip>
                </View>
                <Text style={styles.permitNumber}>#{permit.permitNumber}</Text>
                <View style={styles.permitMeta}>
                  <View style={styles.permitMetaItem}>
                    <Ionicons name="location-outline" size={14} color={colors.gray[500]} />
                    <Text style={styles.permitMetaText}>{permit.location}</Text>
                  </View>
                  <View style={styles.permitMetaItem}>
                    <Ionicons name="warning-outline" size={14} color={getRiskLevelColor(permit.riskLevel)} />
                    <Text style={[styles.permitMetaText, { color: getRiskLevelColor(permit.riskLevel) }]}>
                      {permit.riskLevel}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.primary,
  },
  greeting: {
    color: colors.white,
    fontSize: 16,
    opacity: 0.9,
  },
  userName: {
    color: colors.white,
    fontSize: 24,
    fontWeight: '600',
  },
  qrButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: spacing.md,
    borderRadius: 8,
  },
  connectionStatus: {
    margin: spacing.md,
    padding: spacing.md,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  connectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  connectionText: {
    marginLeft: spacing.xs,
    fontSize: 14,
    fontWeight: '500',
  },
  syncingText: {
    marginLeft: spacing.xs,
    fontSize: 12,
    color: colors.primary,
  },
  lastSyncText: {
    fontSize: 12,
    color: colors.gray[500],
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  statCard: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[600],
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  actionsCard: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: spacing.md,
    color: colors.text,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  permitsCard: {
    margin: spacing.md,
    marginBottom: spacing.xxl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[600],
    marginTop: spacing.md,
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.gray[500],
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  permitItem: {
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  permitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  permitTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    flex: 1,
    marginRight: spacing.md,
  },
  permitNumber: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: spacing.sm,
  },
  permitMeta: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  permitMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permitMetaText: {
    fontSize: 12,
    color: colors.gray[500],
    marginLeft: spacing.xs,
  },
});
