import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Description,
  Warning,
  CheckCircle,
  Schedule,
  Add,
  Refresh,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { format } from 'date-fns';

import { useAuth } from '../contexts/AuthContext';
import { apiService } from '../services/api';
import LoadingScreen from '../components/LoadingScreen';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch dashboard data
  const { data: kpiData, isLoading: kpiLoading, refetch: refetchKPI } = useQuery(
    'dashboard-kpis',
    () => apiService.getKPIDashboard(),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  const { data: recentPermits, isLoading: permitsLoading } = useQuery(
    'recent-permits',
    () => apiService.getPermits({ limit: 5, sortBy: 'createdAt', sortOrder: 'DESC' }),
    {
      refetchInterval: 60000, // Refresh every minute
    }
  );

  if (kpiLoading || permitsLoading) {
    return <LoadingScreen message="Loading dashboard..." />;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'PENDING_APPROVAL': return 'warning';
      case 'APPROVED': return 'info';
      case 'COMPLETED': return 'success';
      case 'CANCELLED': return 'error';
      case 'EXPIRED': return 'error';
      default: return 'default';
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'LOW': return 'success';
      case 'MEDIUM': return 'warning';
      case 'HIGH': return 'error';
      case 'CRITICAL': return 'error';
      default: return 'default';
    }
  };

  const formatTrend = (value: number) => {
    const isPositive = value > 0;
    const isNegative = value < 0;
    
    return {
      icon: isPositive ? <TrendingUp /> : isNegative ? <TrendingDown /> : null,
      color: isPositive ? 'success.main' : isNegative ? 'error.main' : 'text.secondary',
      text: `${isPositive ? '+' : ''}${value.toFixed(1)}%`,
    };
  };

  const kpiCards = [
    {
      title: 'Permits Created',
      value: kpiData?.permitsCreated?.current || 0,
      trend: kpiData?.permitsCreated?.change || 0,
      icon: <Description />,
      color: 'primary.main',
    },
    {
      title: 'Permits Approved',
      value: kpiData?.permitsApproved?.current || 0,
      trend: kpiData?.permitsApproved?.change || 0,
      icon: <CheckCircle />,
      color: 'success.main',
    },
    {
      title: 'High Risk Permits',
      value: kpiData?.highRiskPermits?.current || 0,
      trend: kpiData?.highRiskPermits?.change || 0,
      icon: <Warning />,
      color: 'error.main',
    },
    {
      title: 'Avg Approval Time',
      value: `${kpiData?.averageApprovalTime?.current || 0}h`,
      trend: kpiData?.averageApprovalTime?.change || 0,
      icon: <Schedule />,
      color: 'info.main',
    },
  ];

  return (
    <>
      <Helmet>
        <title>Dashboard - e-Permit Admin</title>
      </Helmet>

      <Box>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h4" fontWeight={700} gutterBottom>
                Good {new Date().getHours() < 12 ? 'morning' : new Date().getHours() < 18 ? 'afternoon' : 'evening'}, {user?.name}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Here's what's happening with your permits today
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={() => refetchKPI()}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => navigate('/permits/create')}
              >
                New Permit
              </Button>
            </Box>
          </Box>
        </Box>

        {/* KPI Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {kpiCards.map((kpi, index) => {
            const trend = formatTrend(kpi.trend);
            return (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          backgroundColor: kpi.color,
                          color: 'white',
                          mr: 2,
                        }}
                      >
                        {kpi.icon}
                      </Avatar>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="h4" fontWeight={700}>
                          {kpi.value}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {kpi.title}
                        </Typography>
                      </Box>
                    </Box>
                    {trend.icon && (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ color: trend.color, mr: 0.5 }}>
                          {trend.icon}
                        </Box>
                        <Typography variant="body2" sx={{ color: trend.color }}>
                          {trend.text} from last period
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        <Grid container spacing={3}>
          {/* Recent Permits */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" fontWeight={600}>
                    Recent Permits
                  </Typography>
                  <Button
                    variant="text"
                    onClick={() => navigate('/permits')}
                  >
                    View All
                  </Button>
                </Box>
                
                {recentPermits?.permits?.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body2" color="text.secondary">
                      No permits found
                    </Typography>
                  </Box>
                ) : (
                  <List>
                    {recentPermits?.permits?.map((permit: any, index: number) => (
                      <ListItem
                        key={permit.id}
                        sx={{
                          cursor: 'pointer',
                          borderRadius: 1,
                          '&:hover': { backgroundColor: 'action.hover' },
                          borderBottom: index < recentPermits.permits.length - 1 ? 1 : 0,
                          borderColor: 'divider',
                        }}
                        onClick={() => navigate(`/permits/${permit.id}`)}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ backgroundColor: 'primary.main' }}>
                            <Description />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle2" fontWeight={600}>
                                {permit.title}
                              </Typography>
                              <Chip
                                label={permit.status.replace('_', ' ')}
                                size="small"
                                color={getStatusColor(permit.status) as any}
                                variant="outlined"
                              />
                              <Chip
                                label={permit.riskLevel}
                                size="small"
                                color={getRiskColor(permit.riskLevel) as any}
                                variant="filled"
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="caption" color="text.secondary">
                                #{permit.permitNumber} • {permit.location}
                              </Typography>
                              <br />
                              <Typography variant="caption" color="text.secondary">
                                Created {format(new Date(permit.createdAt), 'MMM dd, yyyy')}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Quick Actions & Alerts */}
          <Grid item xs={12} lg={4}>
            <Grid container spacing={3}>
              {/* Pending Actions */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" fontWeight={600} gutterBottom>
                      Pending Actions
                    </Typography>
                    
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">
                          Pending Approvals
                        </Typography>
                        <Typography variant="body2" fontWeight={600}>
                          {kpiData?.pendingApprovals || 0}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={Math.min((kpiData?.pendingApprovals || 0) * 10, 100)}
                        color="warning"
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">
                          Overdue Permits
                        </Typography>
                        <Typography variant="body2" fontWeight={600} color="error.main">
                          {kpiData?.overduePermits || 0}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={Math.min((kpiData?.overduePermits || 0) * 20, 100)}
                        color="error"
                      />
                    </Box>

                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={() => navigate('/permits?status=PENDING_APPROVAL')}
                    >
                      Review Pending
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              {/* Quick Actions */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" fontWeight={600} gutterBottom>
                      Quick Actions
                    </Typography>
                    
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Button
                        variant="contained"
                        startIcon={<Add />}
                        onClick={() => navigate('/permits/create')}
                        fullWidth
                      >
                        Create Permit
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={() => navigate('/analytics')}
                        fullWidth
                      >
                        View Analytics
                      </Button>
                      {user?.role === 'ADMIN' && (
                        <Button
                          variant="outlined"
                          onClick={() => navigate('/users')}
                          fullWidth
                        >
                          Manage Users
                        </Button>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default DashboardPage;
