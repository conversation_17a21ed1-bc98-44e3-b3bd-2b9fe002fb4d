import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  HelperText,
} from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import Toast from 'react-native-toast-message';
import { getAuth, sendPasswordResetEmail } from 'firebase/auth';
import { colors, spacing, typography } from '../../theme';

interface ForgotPasswordForm {
  email: string;
}

export default function ForgotPasswordScreen({ navigation }: any) {
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordForm>();

  const onSubmit = async (data: ForgotPasswordForm) => {
    try {
      setLoading(true);
      const auth = getAuth();
      await sendPasswordResetEmail(auth, data.email);
      
      setEmailSent(true);
      Toast.show({
        type: 'success',
        text1: 'Reset Email Sent',
        text2: 'Please check your email for password reset instructions.',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Reset Failed',
        text2: error.message || 'Please check your email and try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  if (emailSent) {
    return (
      <View style={styles.container}>
        <View style={styles.content}>
          <Card style={styles.card}>
            <Card.Content style={styles.successContent}>
              <Text style={styles.successTitle}>Email Sent!</Text>
              <Text style={styles.successDescription}>
                We've sent password reset instructions to your email address.
                Please check your inbox and follow the instructions to reset your password.
              </Text>
              <Button
                mode="contained"
                onPress={() => navigation.goBack()}
                style={styles.backButton}
              >
                Back to Sign In
              </Button>
            </Card.Content>
          </Card>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          <Card style={styles.card}>
            <Card.Content>
              <Text style={styles.title}>Reset Password</Text>
              <Text style={styles.description}>
                Enter your email address and we'll send you instructions to reset your password.
              </Text>

              <Controller
                control={control}
                name="email"
                rules={{
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label="Email"
                    mode="outlined"
                    value={value}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoComplete="email"
                    error={!!errors.email}
                    style={styles.input}
                    left={<TextInput.Icon icon="email" />}
                  />
                )}
              />
              <HelperText type="error" visible={!!errors.email}>
                {errors.email?.message}
              </HelperText>

              <Button
                mode="contained"
                onPress={handleSubmit(onSubmit)}
                loading={loading}
                disabled={loading}
                style={styles.resetButton}
                contentStyle={styles.buttonContent}
              >
                Send Reset Email
              </Button>

              <Button
                mode="text"
                onPress={() => navigation.goBack()}
                style={styles.backToSignInButton}
              >
                Back to Sign In
              </Button>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  content: {
    padding: spacing.lg,
  },
  card: {
    elevation: 4,
    borderRadius: 12,
  },
  title: {
    ...typography.h2,
    textAlign: 'center',
    marginBottom: spacing.sm,
    color: colors.text,
  },
  description: {
    ...typography.body2,
    textAlign: 'center',
    marginBottom: spacing.lg,
    color: colors.textSecondary,
  },
  input: {
    marginBottom: spacing.xs,
  },
  resetButton: {
    marginTop: spacing.lg,
    borderRadius: 8,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  backToSignInButton: {
    marginTop: spacing.md,
  },
  successContent: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  successTitle: {
    ...typography.h2,
    color: colors.success,
    marginBottom: spacing.md,
  },
  successDescription: {
    ...typography.body1,
    textAlign: 'center',
    marginBottom: spacing.lg,
    color: colors.textSecondary,
  },
  backButton: {
    borderRadius: 8,
  },
});
