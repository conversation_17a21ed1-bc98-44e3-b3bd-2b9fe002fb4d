import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/database';

interface AIRecommendationAttributes {
  id: string;
  permitId: string;
  type: 'PPE' | 'SAFETY_STEP' | 'ISOLATION' | 'RISK_ASSESSMENT';
  recommendation: string;
  confidence: number;
  applied: boolean;
  appliedBy?: string;
  appliedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface AIRecommendationCreationAttributes extends Optional<AIRecommendationAttributes, 'id' | 'createdAt' | 'updatedAt' | 'appliedBy' | 'appliedAt'> {}

class AIRecommendation extends Model<AIRecommendationAttributes, AIRecommendationCreationAttributes> implements AIRecommendationAttributes {
  public id!: string;
  public permitId!: string;
  public type!: 'PPE' | 'SAFETY_STEP' | 'ISOLATION' | 'RISK_ASSESSMENT';
  public recommendation!: string;
  public confidence!: number;
  public applied!: boolean;
  public appliedBy?: string;
  public appliedAt?: Date;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

AIRecommendation.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    permitId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'permits',
        key: 'id',
      },
    },
    type: {
      type: DataTypes.ENUM('PPE', 'SAFETY_STEP', 'ISOLATION', 'RISK_ASSESSMENT'),
      allowNull: false,
    },
    recommendation: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    confidence: {
      type: DataTypes.FLOAT,
      allowNull: false,
      validate: {
        min: 0,
        max: 1,
      },
    },
    applied: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    appliedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    appliedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: 'AIRecommendation',
    tableName: 'ai_recommendations',
    indexes: [
      {
        fields: ['permitId'],
      },
      {
        fields: ['type'],
      },
      {
        fields: ['applied'],
      },
      {
        fields: ['confidence'],
      },
    ],
  }
);

export default AIRecommendation;
