import { Router } from 'express';
import {
  createPermit,
  getPermits,
  getPermitById,
  updatePermit,
  approvePermit,
  submitForApproval,
  deletePermit,
} from '../controllers/permitController';
import { authenticateToken, requireRole, requireSiteAccess } from '../middleware/auth';
import {
  validatePermitCreation,
  validatePermitUpdate,
  validatePermitApproval,
  validateUUID,
  validatePagination,
  validatePermitFilters,
  validateDateRange,
} from '../middleware/validation';
import { auditLog, auditMiddleware } from '../middleware/audit';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// Get permits with filtering and pagination
router.get(
  '/',
  validatePagination,
  validatePermitFilters,
  validateDateRange,
  getPermits
);

// Create new permit
router.post(
  '/',
  validatePermitCreation,
  auditLog('Permit', 'CREATE_PERMIT'),
  auditMiddleware,
  createPermit
);

// Get permit by ID
router.get(
  '/:id',
  validateUUID,
  getPermitById
);

// Update permit
router.put(
  '/:id',
  validateUUID,
  validatePermitUpdate,
  auditLog('Permit', 'UPDATE_PERMIT'),
  auditMiddleware,
  updatePermit
);

// Submit permit for approval
router.post(
  '/:id/submit',
  validateUUID,
  auditLog('Permit', 'SUBMIT_FOR_APPROVAL'),
  auditMiddleware,
  submitForApproval
);

// Approve or reject permit
router.post(
  '/:id/approve',
  validateUUID,
  validatePermitApproval,
  requireRole(['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN']),
  auditLog('Permit', 'APPROVE_PERMIT'),
  auditMiddleware,
  approvePermit
);

// Delete permit
router.delete(
  '/:id',
  validateUUID,
  auditLog('Permit', 'DELETE_PERMIT'),
  auditMiddleware,
  deletePermit
);

export default router;
