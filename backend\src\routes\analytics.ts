import { Router } from 'express';
import {
  getPermitAnalytics,
  getSiteAnalytics,
  getKPIDashboard,
  getAuditTrail,
} from '../controllers/analyticsController';
import { authenticateToken, requireRole } from '../middleware/auth';
import { validatePagination, validateDateRange } from '../middleware/validation';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// Get permit analytics
router.get(
  '/permits',
  requireRole(['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN']),
  validateDateRange,
  getPermitAnalytics
);

// Get site analytics
router.get(
  '/sites',
  requireRole(['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN']),
  getSiteAnalytics
);

// Get KPI dashboard
router.get(
  '/kpis',
  requireRole(['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN']),
  getKPIDashboard
);

// Get audit trail
router.get(
  '/audit',
  requireRole(['SAFETY_OFFICER', 'ADMIN']),
  validatePagination,
  validateDateRange,
  getAuditTrail
);

export default router;
