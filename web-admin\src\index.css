/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Chart container styles */
.chart-container {
  position: relative;
  height: 400px;
  width: 100%;
}

.chart-container canvas {
  max-height: 100% !important;
}

/* Data grid custom styles */
.MuiDataGrid-root {
  border: none !important;
}

.MuiDataGrid-columnHeaders {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

.MuiDataGrid-cell {
  border-bottom: 1px solid #f1f5f9 !important;
}

.MuiDataGrid-row:hover {
  background-color: #f8fafc !important;
}

/* Status chip styles */
.status-chip {
  font-weight: 600 !important;
  font-size: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

.status-draft {
  background-color: #f3f4f6 !important;
  color: #374151 !important;
}

.status-pending {
  background-color: #fef3c7 !important;
  color: #92400e !important;
}

.status-approved {
  background-color: #d1fae5 !important;
  color: #065f46 !important;
}

.status-active {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

.status-completed {
  background-color: #d1fae5 !important;
  color: #065f46 !important;
}

.status-cancelled {
  background-color: #fee2e2 !important;
  color: #991b1b !important;
}

.status-expired {
  background-color: #fee2e2 !important;
  color: #991b1b !important;
}

/* Risk level styles */
.risk-low {
  background-color: #d1fae5 !important;
  color: #065f46 !important;
}

.risk-medium {
  background-color: #fef3c7 !important;
  color: #92400e !important;
}

.risk-high {
  background-color: #fed7aa !important;
  color: #9a3412 !important;
}

.risk-critical {
  background-color: #fee2e2 !important;
  color: #991b1b !important;
}

/* Custom button styles */
.btn-primary {
  background-color: #2563eb !important;
  color: white !important;
}

.btn-primary:hover {
  background-color: #1d4ed8 !important;
}

.btn-secondary {
  background-color: #64748b !important;
  color: white !important;
}

.btn-secondary:hover {
  background-color: #475569 !important;
}

.btn-success {
  background-color: #10b981 !important;
  color: white !important;
}

.btn-success:hover {
  background-color: #059669 !important;
}

.btn-warning {
  background-color: #f59e0b !important;
  color: white !important;
}

.btn-warning:hover {
  background-color: #d97706 !important;
}

.btn-error {
  background-color: #ef4444 !important;
  color: white !important;
}

.btn-error:hover {
  background-color: #dc2626 !important;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}
