import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { PermitType, PermitStatus, UserRole, RiskLevel } from '@epermit/shared';

export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
    return;
  }
  next();
};

// User validation rules
export const validateUserCreation = [
  body('email').isEmail().normalizeEmail(),
  body('name').isLength({ min: 2, max: 100 }).trim(),
  body('role').isIn(Object.values(['FIELD_WORKER', 'SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'] as UserRole[])),
  body('siteId').optional().isUUID(),
  body('firebaseUid').notEmpty().trim(),
  handleValidationErrors,
];

export const validateUserUpdate = [
  body('email').optional().isEmail().normalizeEmail(),
  body('name').optional().isLength({ min: 2, max: 100 }).trim(),
  body('role').optional().isIn(Object.values(['FIELD_WORKER', 'SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'] as UserRole[])),
  body('siteId').optional().isUUID(),
  body('isActive').optional().isBoolean(),
  handleValidationErrors,
];

// Site validation rules
export const validateSiteCreation = [
  body('name').isLength({ min: 2, max: 100 }).trim(),
  body('location').isLength({ min: 2, max: 200 }).trim(),
  body('timezone').notEmpty().trim(),
  body('contactEmail').isEmail().normalizeEmail(),
  body('contactPhone').isLength({ min: 10, max: 20 }).trim(),
  handleValidationErrors,
];

export const validateSiteUpdate = [
  body('name').optional().isLength({ min: 2, max: 100 }).trim(),
  body('location').optional().isLength({ min: 2, max: 200 }).trim(),
  body('timezone').optional().notEmpty().trim(),
  body('contactEmail').optional().isEmail().normalizeEmail(),
  body('contactPhone').optional().isLength({ min: 10, max: 20 }).trim(),
  body('isActive').optional().isBoolean(),
  handleValidationErrors,
];

// Permit validation rules
export const validatePermitCreation = [
  body('title').isLength({ min: 5, max: 200 }).trim(),
  body('description').isLength({ min: 10, max: 2000 }).trim(),
  body('type').isIn(Object.values(['HOT_WORK', 'CONFINED_SPACE', 'ELECTRICAL', 'HEIGHT_WORK', 'EXCAVATION', 'CHEMICAL_HANDLING', 'MAINTENANCE', 'OTHER'] as PermitType[])),
  body('location').isLength({ min: 2, max: 200 }).trim(),
  body('siteId').isUUID(),
  body('startDate').isISO8601().toDate(),
  body('endDate').isISO8601().toDate(),
  body('emergencyContact').isLength({ min: 10, max: 20 }).trim(),
  body('isolationRequired').optional().isBoolean(),
  body('isolationSteps').optional().isArray(),
  body('ppeRequired').optional().isArray(),
  body('safetySteps').optional().isArray(),
  body('supervisorId').optional().isUUID(),
  handleValidationErrors,
];

export const validatePermitUpdate = [
  body('title').optional().isLength({ min: 5, max: 200 }).trim(),
  body('description').optional().isLength({ min: 10, max: 2000 }).trim(),
  body('type').optional().isIn(Object.values(['HOT_WORK', 'CONFINED_SPACE', 'ELECTRICAL', 'HEIGHT_WORK', 'EXCAVATION', 'CHEMICAL_HANDLING', 'MAINTENANCE', 'OTHER'] as PermitType[])),
  body('status').optional().isIn(Object.values(['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED', 'EXPIRED'] as PermitStatus[])),
  body('location').optional().isLength({ min: 2, max: 200 }).trim(),
  body('startDate').optional().isISO8601().toDate(),
  body('endDate').optional().isISO8601().toDate(),
  body('actualStartDate').optional().isISO8601().toDate(),
  body('actualEndDate').optional().isISO8601().toDate(),
  body('emergencyContact').optional().isLength({ min: 10, max: 20 }).trim(),
  body('isolationRequired').optional().isBoolean(),
  body('isolationSteps').optional().isArray(),
  body('ppeRequired').optional().isArray(),
  body('safetySteps').optional().isArray(),
  body('supervisorId').optional().isUUID(),
  body('approvalNotes').optional().isLength({ max: 1000 }).trim(),
  body('rejectionReason').optional().isLength({ max: 1000 }).trim(),
  handleValidationErrors,
];

export const validatePermitApproval = [
  body('approved').isBoolean(),
  body('notes').optional().isLength({ max: 1000 }).trim(),
  body('rejectionReason').optional().isLength({ max: 1000 }).trim(),
  handleValidationErrors,
];

// Common validation rules
export const validateUUID = [
  param('id').isUUID(),
  handleValidationErrors,
];

export const validatePagination = [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('sortBy').optional().isString().trim(),
  query('sortOrder').optional().isIn(['ASC', 'DESC']),
  handleValidationErrors,
];

export const validateDateRange = [
  query('startDate').optional().isISO8601().toDate(),
  query('endDate').optional().isISO8601().toDate(),
  handleValidationErrors,
];

export const validatePermitFilters = [
  query('status').optional().isIn(Object.values(['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED', 'EXPIRED'] as PermitStatus[])),
  query('type').optional().isIn(Object.values(['HOT_WORK', 'CONFINED_SPACE', 'ELECTRICAL', 'HEIGHT_WORK', 'EXCAVATION', 'CHEMICAL_HANDLING', 'MAINTENANCE', 'OTHER'] as PermitType[])),
  query('riskLevel').optional().isIn(Object.values(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'] as RiskLevel[])),
  query('siteId').optional().isUUID(),
  query('requestedBy').optional().isUUID(),
  handleValidationErrors,
];

// Voice note validation
export const validateVoiceNote = [
  body('permitId').isUUID(),
  body('audioData').notEmpty(),
  body('duration').isInt({ min: 1, max: 300 }), // Max 5 minutes
  handleValidationErrors,
];

// File upload validation
export const validateFileUpload = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.file) {
    res.status(400).json({
      success: false,
      error: 'No file uploaded',
    });
    return;
  }

  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
  if (!allowedTypes.includes(req.file.mimetype)) {
    res.status(400).json({
      success: false,
      error: 'Invalid file type',
      allowedTypes,
    });
    return;
  }

  const maxSize = 10 * 1024 * 1024; // 10MB
  if (req.file.size > maxSize) {
    res.status(400).json({
      success: false,
      error: 'File too large',
      maxSize: '10MB',
    });
    return;
  }

  next();
};
