#!/bin/bash

# Smart e-Permit Platform Development Setup Script
# This script sets up the entire development environment

set -e  # Exit on any error

echo "🚀 Smart e-Permit Platform Development Setup"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed. You can still run with local PostgreSQL."
        DOCKER_AVAILABLE=false
    else
        DOCKER_AVAILABLE=true
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        if [ "$DOCKER_AVAILABLE" = true ]; then
            print_warning "Docker Compose is not available. Using docker compose instead."
        fi
    fi
    
    print_success "Prerequisites check completed"
}

# Install dependencies for all packages
install_dependencies() {
    print_status "Installing dependencies for all packages..."
    
    # Install root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Install shared package dependencies
    print_status "Installing shared package dependencies..."
    cd shared
    npm install
    npm run build
    cd ..
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # Install mobile app dependencies
    print_status "Installing mobile app dependencies..."
    cd mobile-app
    npm install
    cd ..
    
    # Install web admin dependencies
    print_status "Installing web admin dependencies..."
    cd web-admin
    npm install
    cd ..
    
    print_success "All dependencies installed successfully"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend .env file..."
        cp backend/.env.example backend/.env
        print_warning "Please edit backend/.env with your configuration"
    else
        print_status "Backend .env file already exists"
    fi
    
    # Web admin environment
    if [ ! -f "web-admin/.env.local" ]; then
        print_status "Creating web admin .env.local file..."
        cp web-admin/.env.example web-admin/.env.local
        print_warning "Please edit web-admin/.env.local with your configuration"
    else
        print_status "Web admin .env.local file already exists"
    fi
    
    print_success "Environment files setup completed"
}

# Setup database with Docker
setup_database_docker() {
    print_status "Setting up database with Docker..."
    
    # Start database services
    print_status "Starting PostgreSQL and Redis with Docker..."
    docker-compose -f docker-compose.dev.yml up -d postgres-dev redis-dev
    
    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 10
    
    # Check if database is ready
    until docker-compose -f docker-compose.dev.yml exec -T postgres-dev pg_isready -U epermit_user -d epermit_dev; do
        print_status "Waiting for database..."
        sleep 2
    done
    
    print_success "Database is ready"
    
    # Run migrations
    print_status "Running database migrations..."
    cd backend
    npm run migrate
    cd ..
    
    print_success "Database setup completed with Docker"
}

# Setup database locally
setup_database_local() {
    print_status "Setting up database locally..."
    print_warning "Make sure PostgreSQL is installed and running locally"
    print_warning "Create database 'epermit_dev' and user 'epermit_user' manually"
    
    # Run migrations
    print_status "Running database migrations..."
    cd backend
    npm run migrate
    cd ..
    
    print_success "Database setup completed locally"
}

# Run tests
run_tests() {
    print_status "Running tests for all packages..."
    
    # Test shared package
    print_status "Testing shared package..."
    cd shared
    npm test
    cd ..
    
    # Test backend
    print_status "Testing backend..."
    cd backend
    npm test
    cd ..
    
    # Test web admin
    print_status "Testing web admin..."
    cd web-admin
    npm test
    cd ..
    
    # Test mobile app
    print_status "Testing mobile app..."
    cd mobile-app
    npm test
    cd ..
    
    print_success "All tests completed"
}

# Start development servers
start_dev_servers() {
    print_status "Development servers can be started with:"
    echo ""
    echo "Backend API:"
    echo "  cd backend && npm run dev"
    echo "  Available at: http://localhost:3000"
    echo ""
    echo "Web Admin Dashboard:"
    echo "  cd web-admin && npm run dev"
    echo "  Available at: http://localhost:5173"
    echo ""
    echo "Mobile App:"
    echo "  cd mobile-app && npm start"
    echo "  Follow Expo CLI instructions"
    echo ""
    echo "Database Management (if using Docker):"
    echo "  pgAdmin: http://localhost:5050"
    echo "  Email: <EMAIL>"
    echo "  Password: admin123"
    echo ""
}

# Main setup function
main() {
    echo ""
    print_status "Starting development environment setup..."
    echo ""
    
    # Check prerequisites
    check_prerequisites
    echo ""
    
    # Install dependencies
    install_dependencies
    echo ""
    
    # Setup environment
    setup_environment
    echo ""
    
    # Setup database
    if [ "$DOCKER_AVAILABLE" = true ]; then
        echo "Choose database setup method:"
        echo "1) Docker (Recommended)"
        echo "2) Local PostgreSQL"
        read -p "Enter your choice (1 or 2): " db_choice
        
        case $db_choice in
            1)
                setup_database_docker
                ;;
            2)
                setup_database_local
                ;;
            *)
                print_error "Invalid choice. Please run the script again."
                exit 1
                ;;
        esac
    else
        setup_database_local
    fi
    echo ""
    
    # Ask if user wants to run tests
    read -p "Do you want to run tests? (y/n): " run_tests_choice
    if [ "$run_tests_choice" = "y" ] || [ "$run_tests_choice" = "Y" ]; then
        echo ""
        run_tests
        echo ""
    fi
    
    # Show next steps
    echo ""
    print_success "🎉 Development environment setup completed!"
    echo ""
    start_dev_servers
    
    print_status "Next steps:"
    echo "1. Edit environment files with your Firebase and database credentials"
    echo "2. Start the development servers using the commands above"
    echo "3. Access the applications at the provided URLs"
    echo ""
    print_success "Happy coding! 🚀"
}

# Run main function
main "$@"
