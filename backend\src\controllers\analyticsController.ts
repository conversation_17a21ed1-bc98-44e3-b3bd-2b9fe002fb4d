import { Response } from 'express';
import { Op } from 'sequelize';
import { Permit, User, Site, AuditLog } from '../models';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { PermitAnalytics, SiteAnalytics } from '@epermit/shared';

export const getPermitAnalytics = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  // Check permissions
  if (!['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'].includes(req.user.role)) {
    throw createError('Permission denied to view analytics', 403);
  }

  const { siteId, startDate, endDate } = req.query;

  const where: any = {};
  
  // Site access control
  if (req.user.role !== 'ADMIN') {
    where.siteId = req.user.siteId;
  } else if (siteId) {
    where.siteId = siteId;
  }

  // Date range filter
  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) where.createdAt[Op.gte] = new Date(startDate as string);
    if (endDate) where.createdAt[Op.lte] = new Date(endDate as string);
  }

  // Total permits
  const totalPermits = await Permit.count({ where });

  // Active permits
  const activePermits = await Permit.count({
    where: { ...where, status: 'ACTIVE' },
  });

  // Completed permits
  const completedPermits = await Permit.count({
    where: { ...where, status: 'COMPLETED' },
  });

  // Average approval time
  const approvedPermits = await Permit.findAll({
    where: { ...where, status: { [Op.in]: ['APPROVED', 'ACTIVE', 'COMPLETED'] } },
    attributes: ['createdAt', 'updatedAt'],
  });

  const averageApprovalTime = approvedPermits.length > 0
    ? approvedPermits.reduce((sum, permit) => {
        const approvalTime = permit.updatedAt.getTime() - permit.createdAt.getTime();
        return sum + approvalTime;
      }, 0) / approvedPermits.length / (1000 * 60 * 60) // Convert to hours
    : 0;

  // Risk distribution
  const riskDistribution = await Permit.findAll({
    where,
    attributes: [
      'riskLevel',
      [Permit.sequelize!.fn('COUNT', Permit.sequelize!.col('id')), 'count'],
    ],
    group: ['riskLevel'],
    raw: true,
  });

  const riskDistributionMap = riskDistribution.reduce((acc: any, item: any) => {
    acc[item.riskLevel] = parseInt(item.count);
    return acc;
  }, {});

  // Type distribution
  const typeDistribution = await Permit.findAll({
    where,
    attributes: [
      'type',
      [Permit.sequelize!.fn('COUNT', Permit.sequelize!.col('id')), 'count'],
    ],
    group: ['type'],
    raw: true,
  });

  const typeDistributionMap = typeDistribution.reduce((acc: any, item: any) => {
    acc[item.type] = parseInt(item.count);
    return acc;
  }, {});

  // Monthly trends (last 12 months)
  const twelveMonthsAgo = new Date();
  twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

  const monthlyTrends = await Permit.findAll({
    where: {
      ...where,
      createdAt: { [Op.gte]: twelveMonthsAgo },
    },
    attributes: [
      [Permit.sequelize!.fn('DATE_TRUNC', 'month', Permit.sequelize!.col('createdAt')), 'month'],
      [Permit.sequelize!.fn('COUNT', Permit.sequelize!.col('id')), 'permits'],
    ],
    group: [Permit.sequelize!.fn('DATE_TRUNC', 'month', Permit.sequelize!.col('createdAt'))],
    order: [[Permit.sequelize!.fn('DATE_TRUNC', 'month', Permit.sequelize!.col('createdAt')), 'ASC']],
    raw: true,
  });

  const analytics: PermitAnalytics = {
    totalPermits,
    activePermits,
    completedPermits,
    averageApprovalTime: Math.round(averageApprovalTime * 100) / 100,
    riskDistribution: riskDistributionMap,
    typeDistribution: typeDistributionMap,
    monthlyTrends: monthlyTrends.map((trend: any) => ({
      month: new Date(trend.month).toISOString().slice(0, 7),
      permits: parseInt(trend.permits),
      incidents: 0, // TODO: Implement incident tracking
    })),
  };

  res.json({
    success: true,
    data: analytics,
  });
});

export const getSiteAnalytics = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  // Check permissions
  if (!['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'].includes(req.user.role)) {
    throw createError('Permission denied to view analytics', 403);
  }

  const sites = req.user.role === 'ADMIN' 
    ? await Site.findAll({ where: { isActive: true } })
    : await Site.findAll({ where: { id: req.user.siteId, isActive: true } });

  const siteAnalytics: SiteAnalytics[] = [];

  for (const site of sites) {
    const totalPermits = await Permit.count({
      where: { siteId: site.id },
    });

    // Calculate risk score based on permit risk levels
    const riskData = await Permit.findAll({
      where: { siteId: site.id },
      attributes: [
        'riskLevel',
        [Permit.sequelize!.fn('COUNT', Permit.sequelize!.col('id')), 'count'],
      ],
      group: ['riskLevel'],
      raw: true,
    });

    const riskScore = riskData.reduce((score: number, item: any) => {
      const weights = { LOW: 1, MEDIUM: 2, HIGH: 3, CRITICAL: 4 };
      return score + (weights[item.riskLevel as keyof typeof weights] * parseInt(item.count));
    }, 0) / Math.max(totalPermits, 1);

    // Calculate compliance score based on completed vs total permits
    const completedPermits = await Permit.count({
      where: { siteId: site.id, status: 'COMPLETED' },
    });

    const complianceScore = totalPermits > 0 ? (completedPermits / totalPermits) * 100 : 100;

    siteAnalytics.push({
      siteId: site.id,
      siteName: site.name,
      totalPermits,
      riskScore: Math.round(riskScore * 100) / 100,
      incidentRate: 0, // TODO: Implement incident tracking
      complianceScore: Math.round(complianceScore * 100) / 100,
    });
  }

  res.json({
    success: true,
    data: siteAnalytics,
  });
});

export const getKPIDashboard = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  // Check permissions
  if (!['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'].includes(req.user.role)) {
    throw createError('Permission denied to view KPI dashboard', 403);
  }

  const { siteId } = req.query;
  const where: any = {};

  // Site access control
  if (req.user.role !== 'ADMIN') {
    where.siteId = req.user.siteId;
  } else if (siteId) {
    where.siteId = siteId;
  }

  // Current period (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Previous period (30-60 days ago)
  const sixtyDaysAgo = new Date();
  sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

  const currentPeriodWhere = { ...where, createdAt: { [Op.gte]: thirtyDaysAgo } };
  const previousPeriodWhere = { 
    ...where, 
    createdAt: { 
      [Op.gte]: sixtyDaysAgo,
      [Op.lt]: thirtyDaysAgo,
    },
  };

  // Permits created
  const currentPermits = await Permit.count({ where: currentPeriodWhere });
  const previousPermits = await Permit.count({ where: previousPeriodWhere });
  const permitsChange = previousPermits > 0 ? ((currentPermits - previousPermits) / previousPermits) * 100 : 0;

  // Permits approved
  const currentApproved = await Permit.count({
    where: { ...currentPeriodWhere, status: { [Op.in]: ['APPROVED', 'ACTIVE', 'COMPLETED'] } },
  });
  const previousApproved = await Permit.count({
    where: { ...previousPeriodWhere, status: { [Op.in]: ['APPROVED', 'ACTIVE', 'COMPLETED'] } },
  });
  const approvedChange = previousApproved > 0 ? ((currentApproved - previousApproved) / previousApproved) * 100 : 0;

  // High risk permits
  const currentHighRisk = await Permit.count({
    where: { ...currentPeriodWhere, riskLevel: { [Op.in]: ['HIGH', 'CRITICAL'] } },
  });
  const previousHighRisk = await Permit.count({
    where: { ...previousPeriodWhere, riskLevel: { [Op.in]: ['HIGH', 'CRITICAL'] } },
  });
  const highRiskChange = previousHighRisk > 0 ? ((currentHighRisk - previousHighRisk) / previousHighRisk) * 100 : 0;

  // Average approval time
  const currentApprovals = await Permit.findAll({
    where: { ...currentPeriodWhere, status: { [Op.in]: ['APPROVED', 'ACTIVE', 'COMPLETED'] } },
    attributes: ['createdAt', 'updatedAt'],
  });

  const currentAvgTime = currentApprovals.length > 0
    ? currentApprovals.reduce((sum, permit) => {
        return sum + (permit.updatedAt.getTime() - permit.createdAt.getTime());
      }, 0) / currentApprovals.length / (1000 * 60 * 60)
    : 0;

  const previousApprovals = await Permit.findAll({
    where: { ...previousPeriodWhere, status: { [Op.in]: ['APPROVED', 'ACTIVE', 'COMPLETED'] } },
    attributes: ['createdAt', 'updatedAt'],
  });

  const previousAvgTime = previousApprovals.length > 0
    ? previousApprovals.reduce((sum, permit) => {
        return sum + (permit.updatedAt.getTime() - permit.createdAt.getTime());
      }, 0) / previousApprovals.length / (1000 * 60 * 60)
    : 0;

  const approvalTimeChange = previousAvgTime > 0 ? ((currentAvgTime - previousAvgTime) / previousAvgTime) * 100 : 0;

  // Pending approvals
  const pendingApprovals = await Permit.count({
    where: { ...where, status: 'PENDING_APPROVAL' },
  });

  // Overdue permits
  const now = new Date();
  const overduePermits = await Permit.count({
    where: {
      ...where,
      status: { [Op.in]: ['APPROVED', 'ACTIVE'] },
      endDate: { [Op.lt]: now },
    },
  });

  const kpis = {
    permitsCreated: {
      current: currentPermits,
      change: Math.round(permitsChange * 100) / 100,
    },
    permitsApproved: {
      current: currentApproved,
      change: Math.round(approvedChange * 100) / 100,
    },
    highRiskPermits: {
      current: currentHighRisk,
      change: Math.round(highRiskChange * 100) / 100,
    },
    averageApprovalTime: {
      current: Math.round(currentAvgTime * 100) / 100,
      change: Math.round(approvalTimeChange * 100) / 100,
    },
    pendingApprovals,
    overduePermits,
  };

  res.json({
    success: true,
    data: kpis,
  });
});

export const getAuditTrail = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  // Check permissions
  if (!['SAFETY_OFFICER', 'ADMIN'].includes(req.user.role)) {
    throw createError('Permission denied to view audit trail', 403);
  }

  const {
    page = 1,
    limit = 20,
    entityType,
    entityId,
    action,
    userId,
    startDate,
    endDate,
  } = req.query;

  const offset = (Number(page) - 1) * Number(limit);
  const where: any = {};

  if (entityType) where.entityType = entityType;
  if (entityId) where.entityId = entityId;
  if (action) where.action = action;
  if (userId) where.userId = userId;

  if (startDate || endDate) {
    where.timestamp = {};
    if (startDate) where.timestamp[Op.gte] = new Date(startDate as string);
    if (endDate) where.timestamp[Op.lte] = new Date(endDate as string);
  }

  const { count, rows: auditLogs } = await AuditLog.findAndCountAll({
    where,
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'role'],
      },
    ],
    order: [['timestamp', 'DESC']],
    limit: Number(limit),
    offset,
  });

  res.json({
    success: true,
    data: {
      auditLogs,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count,
        pages: Math.ceil(count / Number(limit)),
      },
    },
  });
});
