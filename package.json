{"name": "epermit-platform", "version": "1.0.0", "description": "Smart e-Permit Platform for Industrial MNCs", "private": true, "workspaces": ["backend", "mobile-app", "web-admin", "shared"], "scripts": {"install:all": "npm install && npm run install:backend && npm run install:mobile && npm run install:web", "install:backend": "cd backend && npm install", "install:mobile": "cd mobile-app && npm install", "install:web": "cd web-admin && npm install", "install:shared": "cd shared && npm install", "dev:backend": "cd backend && npm run dev", "dev:mobile": "cd mobile-app && expo start", "dev:web": "cd web-admin && npm run dev", "build:backend": "cd backend && npm run build", "build:mobile": "cd mobile-app && expo build", "build:web": "cd web-admin && npm run build", "test:all": "npm run test:backend && npm run test:mobile && npm run test:web", "test:backend": "cd backend && npm test", "test:mobile": "cd mobile-app && npm test", "test:web": "cd web-admin && npm test", "lint:all": "npm run lint:backend && npm run lint:mobile && npm run lint:web", "lint:backend": "cd backend && npm run lint", "lint:mobile": "cd mobile-app && npm run lint", "lint:web": "cd web-admin && npm run lint", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build", "migrate": "cd backend && npm run migrate", "seed": "cd backend && npm run seed"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/epermit-platform.git"}, "keywords": ["permit", "industrial", "safety", "mobile", "ai", "react-native", "nodejs", "postgresql"], "author": "Your Organization", "license": "MIT"}