import { Response } from 'express';
import { User, Site } from '../models';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { createAuditLog } from '../middleware/audit';

export const getCurrentUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const user = await User.findByPk(req.user.id, {
    include: [
      {
        model: Site,
        as: 'site',
        attributes: ['id', 'name', 'location', 'timezone'],
      },
    ],
    attributes: { exclude: ['firebaseUid'] },
  });

  if (!user) {
    throw createError('User not found', 404);
  }

  res.json({
    success: true,
    data: user,
  });
});

export const updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { name } = req.body;
  
  const user = await User.findByPk(req.user.id);
  if (!user) {
    throw createError('User not found', 404);
  }

  const originalData = user.toJSON();
  
  await user.update({ name });

  await createAuditLog(
    req.user.id,
    'User',
    user.id,
    'UPDATE_PROFILE',
    {
      before: originalData,
      after: user.toJSON(),
    },
    req.ip,
    req.get('User-Agent')
  );

  res.json({
    success: true,
    data: user,
    message: 'Profile updated successfully',
  });
});

export const registerUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { email, name, role, siteId, firebaseUid } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    where: { email },
  });

  if (existingUser) {
    throw createError('User already exists', 409);
  }

  // Validate site if provided
  if (siteId) {
    const site = await Site.findByPk(siteId);
    if (!site) {
      throw createError('Invalid site ID', 400);
    }
  }

  const user = await User.create({
    email,
    name,
    role,
    siteId,
    firebaseUid,
  });

  if (req.user) {
    await createAuditLog(
      req.user.id,
      'User',
      user.id,
      'CREATE_USER',
      {
        created: user.toJSON(),
      },
      req.ip,
      req.get('User-Agent')
    );
  }

  res.status(201).json({
    success: true,
    data: user,
    message: 'User registered successfully',
  });
});

export const getUserPermissions = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const permissions = {
    canCreatePermit: ['FIELD_WORKER', 'SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'].includes(req.user.role),
    canApprovePermit: ['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'].includes(req.user.role),
    canManageUsers: ['ADMIN'].includes(req.user.role),
    canManageSites: ['ADMIN'].includes(req.user.role),
    canViewAnalytics: ['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'].includes(req.user.role),
    canAccessAllSites: ['ADMIN'].includes(req.user.role),
    canManageAI: ['SAFETY_OFFICER', 'ADMIN'].includes(req.user.role),
  };

  res.json({
    success: true,
    data: {
      user: req.user,
      permissions,
    },
  });
});
