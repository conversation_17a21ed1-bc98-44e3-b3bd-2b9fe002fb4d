{"name": "@epermit/backend", "version": "1.0.0", "description": "Backend API for e-Permit Platform", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "db:create": "npx sequelize-cli db:create", "db:drop": "npx sequelize-cli db:drop"}, "dependencies": {"@epermit/shared": "^1.0.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.1", "bcryptjs": "^2.4.3", "pg": "^8.11.1", "sequelize": "^6.32.1", "sequelize-cli": "^6.6.1", "dotenv": "^16.3.1", "firebase-admin": "^11.10.1", "qrcode": "^1.5.3", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "node-cron": "^3.0.2", "winston": "^3.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/compression": "^1.7.2", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/pg": "^8.10.2", "@types/qrcode": "^1.5.1", "@types/multer": "^1.4.7", "@types/node-cron": "^3.0.8", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/jest": "^29.5.3", "@types/supertest": "^2.0.12", "typescript": "^5.1.6", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.6.1", "ts-jest": "^29.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}