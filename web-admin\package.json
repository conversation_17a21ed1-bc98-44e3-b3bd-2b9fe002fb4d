{"name": "@epermit/web-admin", "version": "1.0.0", "description": "e-Permit Web Admin Dashboard", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@epermit/shared": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "react-query": "^3.39.3", "axios": "^1.4.0", "firebase": "^10.1.0", "@mui/material": "^5.14.1", "@mui/icons-material": "^5.14.1", "@mui/x-data-grid": "^6.10.1", "@mui/x-date-pickers": "^6.10.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "chart.js": "^4.3.3", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "react-hook-form": "^7.45.2", "@hookform/resolvers": "^3.1.1", "zod": "^3.22.2", "react-hot-toast": "^2.4.1", "recharts": "^2.7.2", "react-helmet-async": "^1.3.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.1", "@vitest/ui": "^0.34.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "jsdom": "^22.1.0"}, "engines": {"node": ">=18.0.0"}}