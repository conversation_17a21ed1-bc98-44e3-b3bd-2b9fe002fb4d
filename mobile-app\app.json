{"expo": {"name": "e-Permit Mobile", "slug": "epermit-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.epermit.mobile", "buildNumber": "1.0.0", "infoPlist": {"NSCameraUsageDescription": "This app uses camera to scan QR codes and take photos for permits", "NSMicrophoneUsageDescription": "This app uses microphone to record voice notes for permits", "NSLocationWhenInUseUsageDescription": "This app uses location to automatically fill permit location information"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.epermit.mobile", "versionCode": 1, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-camera", "expo-barcode-scanner", "expo-av", "expo-sqlite", "expo-notifications", "expo-location", ["expo-build-properties", {"android": {"compileSdkVersion": 33, "targetSdkVersion": 33, "buildToolsVersion": "33.0.0"}, "ios": {"deploymentTarget": "13.0"}}]], "extra": {"eas": {"projectId": "your-eas-project-id"}}}}