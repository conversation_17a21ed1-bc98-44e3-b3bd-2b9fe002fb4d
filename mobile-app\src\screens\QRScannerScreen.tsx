import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';
import {
  Text,
  Button,
  Surface,
  ActivityIndicator,
} from 'react-native-paper';
import { BarCodeScanner } from 'expo-barcode-scanner';
import { Camera } from 'expo-camera';
import Toast from 'react-native-toast-message';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing } from '../theme';
import { apiService } from '../services/api';
import { Permit } from '@epermit/shared';

const { width, height } = Dimensions.get('window');

export default function QRScannerScreen({ navigation }: any) {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [loading, setLoading] = useState(false);
  const [flashOn, setFlashOn] = useState(false);

  useEffect(() => {
    const getBarCodeScannerPermissions = async () => {
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      setHasPermission(status === 'granted');
    };

    getBarCodeScannerPermissions();
  }, []);

  const handleBarCodeScanned = async ({ type, data }: { type: string; data: string }) => {
    if (scanned) return;
    
    setScanned(true);
    setLoading(true);

    try {
      // Parse QR code data
      let qrData;
      try {
        qrData = JSON.parse(data);
      } catch (error) {
        throw new Error('Invalid QR code format');
      }

      // Verify QR code contains permit information
      if (!qrData.permitId || !qrData.permitNumber) {
        throw new Error('QR code does not contain valid permit information');
      }

      // Verify permit with backend
      const permit: Permit = await apiService.verifyQRCode(data);

      Toast.show({
        type: 'success',
        text1: 'Permit Found',
        text2: `Permit #${permit.permitNumber} verified successfully`,
      });

      // Navigate to permit details
      navigation.navigate('Permits', {
        screen: 'PermitDetail',
        params: { permitId: permit.id }
      });

    } catch (error: any) {
      console.error('QR scan error:', error);
      
      Alert.alert(
        'Scan Failed',
        error.message || 'Failed to verify QR code. Please try again.',
        [
          {
            text: 'Try Again',
            onPress: () => {
              setScanned(false);
              setLoading(false);
            }
          },
          {
            text: 'Cancel',
            onPress: () => navigation.goBack(),
            style: 'cancel'
          }
        ]
      );
    }
  };

  const toggleFlash = () => {
    setFlashOn(!flashOn);
  };

  const resetScanner = () => {
    setScanned(false);
    setLoading(false);
  };

  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <View style={styles.permissionContainer}>
          <Ionicons name="camera-off" size={64} color={colors.gray[400]} />
          <Text style={styles.permissionTitle}>Camera Permission Required</Text>
          <Text style={styles.permissionText}>
            Please grant camera permission to scan QR codes for permit verification.
          </Text>
          <Button
            mode="contained"
            onPress={() => BarCodeScanner.requestPermissionsAsync()}
            style={styles.permissionButton}
          >
            Grant Permission
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <BarCodeScanner
        onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
        style={styles.scanner}
        flashMode={flashOn ? Camera.Constants.FlashMode.torch : Camera.Constants.FlashMode.off}
      />

      {/* Overlay */}
      <View style={styles.overlay}>
        {/* Top overlay */}
        <View style={styles.overlayTop}>
          <Text style={styles.instructionText}>
            Position the QR code within the frame to scan
          </Text>
        </View>

        {/* Scanner frame */}
        <View style={styles.scannerFrame}>
          <View style={styles.scannerContainer}>
            {/* Corner indicators */}
            <View style={[styles.corner, styles.topLeft]} />
            <View style={[styles.corner, styles.topRight]} />
            <View style={[styles.corner, styles.bottomLeft]} />
            <View style={[styles.corner, styles.bottomRight]} />
            
            {loading && (
              <View style={styles.loadingOverlay}>
                <ActivityIndicator size="large" color={colors.white} />
                <Text style={styles.loadingOverlayText}>Verifying permit...</Text>
              </View>
            )}
          </View>
        </View>

        {/* Bottom overlay */}
        <View style={styles.overlayBottom}>
          <Surface style={styles.controlsContainer}>
            <Button
              mode="outlined"
              icon={flashOn ? "flash" : "flash-off"}
              onPress={toggleFlash}
              style={styles.controlButton}
              textColor={colors.white}
            >
              {flashOn ? 'Flash On' : 'Flash Off'}
            </Button>
            
            {scanned && !loading && (
              <Button
                mode="contained"
                icon="refresh"
                onPress={resetScanner}
                style={styles.controlButton}
              >
                Scan Again
              </Button>
            )}
          </Surface>

          <Text style={styles.helpText}>
            Make sure the QR code is well-lit and clearly visible
          </Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanner: {
    ...StyleSheet.absoluteFillObject,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'space-between',
  },
  overlayTop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingBottom: spacing.lg,
  },
  overlayBottom: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: spacing.lg,
  },
  scannerFrame: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  scannerContainer: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: colors.white,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderTopWidth: 3,
    borderLeftWidth: 3,
  },
  topRight: {
    top: 0,
    right: 0,
    borderTopWidth: 3,
    borderRightWidth: 3,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderBottomWidth: 3,
    borderRightWidth: 3,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlayText: {
    color: colors.white,
    marginTop: spacing.md,
    fontSize: 16,
  },
  instructionText: {
    color: colors.white,
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: spacing.lg,
  },
  controlsContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    marginHorizontal: spacing.lg,
  },
  controlButton: {
    flex: 1,
  },
  helpText: {
    color: colors.white,
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: spacing.lg,
    marginTop: spacing.lg,
    opacity: 0.8,
  },
  loadingText: {
    color: colors.white,
    marginTop: spacing.md,
    fontSize: 16,
  },
  permissionContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.white,
    marginTop: spacing.lg,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: colors.white,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.xl,
    opacity: 0.9,
  },
  permissionButton: {
    borderRadius: 8,
  },
});
