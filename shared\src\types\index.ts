import { z } from 'zod';

// User Types
export const UserRoleSchema = z.enum(['FIELD_WORKER', 'SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN']);
export type UserRole = z.infer<typeof UserRoleSchema>;

export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string(),
  role: UserRoleSchema,
  siteId: z.string().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});
export type User = z.infer<typeof UserSchema>;

// Site Types
export const SiteSchema = z.object({
  id: z.string(),
  name: z.string(),
  location: z.string(),
  timezone: z.string(),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});
export type Site = z.infer<typeof SiteSchema>;

// Permit Types
export const PermitStatusSchema = z.enum([
  'DRAFT',
  'PENDING_APPROVAL',
  'APPROVED',
  'ACTIVE',
  'COMPLETED',
  'CANCELLED',
  'EXPIRED'
]);
export type PermitStatus = z.infer<typeof PermitStatusSchema>;

export const PermitTypeSchema = z.enum([
  'HOT_WORK',
  'CONFINED_SPACE',
  'ELECTRICAL',
  'HEIGHT_WORK',
  'EXCAVATION',
  'CHEMICAL_HANDLING',
  'MAINTENANCE',
  'OTHER'
]);
export type PermitType = z.infer<typeof PermitTypeSchema>;

export const RiskLevelSchema = z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']);
export type RiskLevel = z.infer<typeof RiskLevelSchema>;

export const PPEItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  required: z.boolean(),
  checked: z.boolean().default(false),
});
export type PPEItem = z.infer<typeof PPEItemSchema>;

export const SafetyStepSchema = z.object({
  id: z.string(),
  description: z.string(),
  completed: z.boolean().default(false),
  completedBy: z.string().optional(),
  completedAt: z.date().optional(),
});
export type SafetyStep = z.infer<typeof SafetyStepSchema>;

export const PermitSchema = z.object({
  id: z.string(),
  permitNumber: z.string(),
  title: z.string(),
  description: z.string(),
  type: PermitTypeSchema,
  status: PermitStatusSchema,
  riskLevel: RiskLevelSchema,
  location: z.string(),
  siteId: z.string(),
  requestedBy: z.string(),
  approvedBy: z.string().optional(),
  supervisorId: z.string().optional(),
  startDate: z.date(),
  endDate: z.date(),
  actualStartDate: z.date().optional(),
  actualEndDate: z.date().optional(),
  ppeRequired: z.array(PPEItemSchema),
  safetySteps: z.array(SafetyStepSchema),
  isolationRequired: z.boolean().default(false),
  isolationSteps: z.array(z.string()).optional(),
  emergencyContact: z.string(),
  qrCode: z.string().optional(),
  voiceNotes: z.array(z.string()).optional(),
  attachments: z.array(z.string()).optional(),
  aiRecommendations: z.array(z.string()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});
export type Permit = z.infer<typeof PermitSchema>;

// AI Recommendation Types
export const AIRecommendationSchema = z.object({
  id: z.string(),
  permitId: z.string(),
  type: z.enum(['PPE', 'SAFETY_STEP', 'ISOLATION', 'RISK_ASSESSMENT']),
  recommendation: z.string(),
  confidence: z.number().min(0).max(1),
  applied: z.boolean().default(false),
  createdAt: z.date(),
});
export type AIRecommendation = z.infer<typeof AIRecommendationSchema>;

// Audit Log Types
export const AuditLogSchema = z.object({
  id: z.string(),
  entityType: z.string(),
  entityId: z.string(),
  action: z.string(),
  changes: z.record(z.any()),
  userId: z.string(),
  timestamp: z.date(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});
export type AuditLog = z.infer<typeof AuditLogSchema>;

// API Response Types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
});
export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// Notification Types
export const NotificationTypeSchema = z.enum([
  'PERMIT_CREATED',
  'PERMIT_APPROVED',
  'PERMIT_REJECTED',
  'PERMIT_EXPIRED',
  'PERMIT_COMPLETED',
  'SAFETY_ALERT'
]);
export type NotificationType = z.infer<typeof NotificationTypeSchema>;

export const NotificationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  type: NotificationTypeSchema,
  title: z.string(),
  message: z.string(),
  data: z.record(z.any()).optional(),
  read: z.boolean().default(false),
  createdAt: z.date(),
});
export type Notification = z.infer<typeof NotificationSchema>;

// Analytics Types
export interface PermitAnalytics {
  totalPermits: number;
  activePermits: number;
  completedPermits: number;
  averageApprovalTime: number;
  riskDistribution: Record<RiskLevel, number>;
  typeDistribution: Record<PermitType, number>;
  monthlyTrends: Array<{
    month: string;
    permits: number;
    incidents: number;
  }>;
}

export interface SiteAnalytics {
  siteId: string;
  siteName: string;
  totalPermits: number;
  riskScore: number;
  incidentRate: number;
  complianceScore: number;
}
