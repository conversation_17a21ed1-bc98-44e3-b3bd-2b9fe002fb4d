import { PermitType, RiskLevel, PPEItem, SafetyStep } from '../types';

// Utility functions for permit management

export const generatePermitNumber = (type: PermitType, siteId: string): string => {
  const typePrefix = {
    HOT_WORK: 'HW',
    CONFINED_SPACE: 'CS',
    ELECTRICAL: 'EL',
    HEIGHT_WORK: 'HT',
    EXCAVATION: 'EX',
    CHEMICAL_HANDLING: 'CH',
    MAINTENANCE: 'MT',
    OTHER: 'OT'
  };

  const timestamp = Date.now().toString().slice(-6);
  const sitePrefix = siteId.slice(0, 3).toUpperCase();
  
  return `${typePrefix[type]}-${sitePrefix}-${timestamp}`;
};

export const calculateRiskLevel = (
  permitType: PermitType,
  location: string,
  isolationRequired: boolean,
  ppeCount: number
): RiskLevel => {
  let score = 0;

  // Base risk by permit type
  const typeRisk = {
    HOT_WORK: 3,
    CONFINED_SPACE: 4,
    ELECTRICAL: 3,
    HEIGHT_WORK: 3,
    EXCAVATION: 2,
    CHEMICAL_HANDLING: 4,
    MAINTENANCE: 1,
    OTHER: 1
  };

  score += typeRisk[permitType];

  // Location risk factors
  if (location.toLowerCase().includes('outdoor')) score += 1;
  if (location.toLowerCase().includes('basement')) score += 2;
  if (location.toLowerCase().includes('roof')) score += 2;

  // Safety measures
  if (isolationRequired) score += 1;
  if (ppeCount < 3) score += 1;

  if (score <= 2) return 'LOW';
  if (score <= 4) return 'MEDIUM';
  if (score <= 6) return 'HIGH';
  return 'CRITICAL';
};

export const getDefaultPPE = (permitType: PermitType): PPEItem[] => {
  const basePPE: PPEItem[] = [
    { id: 'ppe-1', name: 'Safety Helmet', required: true, checked: false },
    { id: 'ppe-2', name: 'Safety Boots', required: true, checked: false },
    { id: 'ppe-3', name: 'High-Vis Vest', required: true, checked: false },
  ];

  const typePPE: Record<PermitType, PPEItem[]> = {
    HOT_WORK: [
      { id: 'ppe-4', name: 'Fire Resistant Clothing', required: true, checked: false },
      { id: 'ppe-5', name: 'Welding Mask', required: true, checked: false },
      { id: 'ppe-6', name: 'Heat Resistant Gloves', required: true, checked: false },
    ],
    CONFINED_SPACE: [
      { id: 'ppe-7', name: 'Gas Monitor', required: true, checked: false },
      { id: 'ppe-8', name: 'Breathing Apparatus', required: true, checked: false },
      { id: 'ppe-9', name: 'Safety Harness', required: true, checked: false },
    ],
    ELECTRICAL: [
      { id: 'ppe-10', name: 'Insulated Gloves', required: true, checked: false },
      { id: 'ppe-11', name: 'Arc Flash Suit', required: true, checked: false },
      { id: 'ppe-12', name: 'Voltage Tester', required: true, checked: false },
    ],
    HEIGHT_WORK: [
      { id: 'ppe-13', name: 'Safety Harness', required: true, checked: false },
      { id: 'ppe-14', name: 'Fall Arrest System', required: true, checked: false },
      { id: 'ppe-15', name: 'Non-slip Shoes', required: true, checked: false },
    ],
    EXCAVATION: [
      { id: 'ppe-16', name: 'Hard Hat with Chin Strap', required: true, checked: false },
      { id: 'ppe-17', name: 'Steel Toe Boots', required: true, checked: false },
      { id: 'ppe-18', name: 'Reflective Vest', required: true, checked: false },
    ],
    CHEMICAL_HANDLING: [
      { id: 'ppe-19', name: 'Chemical Resistant Gloves', required: true, checked: false },
      { id: 'ppe-20', name: 'Chemical Goggles', required: true, checked: false },
      { id: 'ppe-21', name: 'Respirator', required: true, checked: false },
    ],
    MAINTENANCE: [
      { id: 'ppe-22', name: 'Work Gloves', required: true, checked: false },
      { id: 'ppe-23', name: 'Safety Glasses', required: true, checked: false },
    ],
    OTHER: []
  };

  return [...basePPE, ...typePPE[permitType]];
};

export const getDefaultSafetySteps = (permitType: PermitType): SafetyStep[] => {
  const baseSteps: SafetyStep[] = [
    { id: 'step-1', description: 'Conduct pre-work safety briefing', completed: false },
    { id: 'step-2', description: 'Verify all PPE is worn correctly', completed: false },
    { id: 'step-3', description: 'Establish emergency communication', completed: false },
  ];

  const typeSteps: Record<PermitType, SafetyStep[]> = {
    HOT_WORK: [
      { id: 'step-4', description: 'Clear area of flammable materials', completed: false },
      { id: 'step-5', description: 'Have fire extinguisher ready', completed: false },
      { id: 'step-6', description: 'Post fire watch personnel', completed: false },
    ],
    CONFINED_SPACE: [
      { id: 'step-7', description: 'Test atmosphere for gases', completed: false },
      { id: 'step-8', description: 'Establish ventilation', completed: false },
      { id: 'step-9', description: 'Station attendant outside', completed: false },
    ],
    ELECTRICAL: [
      { id: 'step-10', description: 'Verify lockout/tagout procedures', completed: false },
      { id: 'step-11', description: 'Test circuits are de-energized', completed: false },
      { id: 'step-12', description: 'Use proper insulated tools', completed: false },
    ],
    HEIGHT_WORK: [
      { id: 'step-13', description: 'Inspect fall protection equipment', completed: false },
      { id: 'step-14', description: 'Secure work area below', completed: false },
      { id: 'step-15', description: 'Check weather conditions', completed: false },
    ],
    EXCAVATION: [
      { id: 'step-16', description: 'Call utility location service', completed: false },
      { id: 'step-17', description: 'Inspect excavation walls', completed: false },
      { id: 'step-18', description: 'Provide safe entry/exit', completed: false },
    ],
    CHEMICAL_HANDLING: [
      { id: 'step-19', description: 'Review safety data sheets', completed: false },
      { id: 'step-20', description: 'Ensure proper ventilation', completed: false },
      { id: 'step-21', description: 'Have spill kit available', completed: false },
    ],
    MAINTENANCE: [
      { id: 'step-22', description: 'Isolate equipment energy sources', completed: false },
      { id: 'step-23', description: 'Use proper tools for the job', completed: false },
    ],
    OTHER: []
  };

  return [...baseSteps, ...typeSteps[permitType]];
};

export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const isPermitExpired = (permit: { endDate: Date; status: string }): boolean => {
  return new Date() > permit.endDate && permit.status !== 'COMPLETED';
};

export const getPermitStatusColor = (status: string): string => {
  const colors = {
    DRAFT: '#6B7280',
    PENDING_APPROVAL: '#F59E0B',
    APPROVED: '#10B981',
    ACTIVE: '#3B82F6',
    COMPLETED: '#059669',
    CANCELLED: '#EF4444',
    EXPIRED: '#DC2626'
  };
  return colors[status as keyof typeof colors] || '#6B7280';
};

export const validatePermitDates = (startDate: Date, endDate: Date): string | null => {
  const now = new Date();
  
  if (startDate < now) {
    return 'Start date cannot be in the past';
  }
  
  if (endDate <= startDate) {
    return 'End date must be after start date';
  }
  
  const maxDuration = 30 * 24 * 60 * 60 * 1000; // 30 days
  if (endDate.getTime() - startDate.getTime() > maxDuration) {
    return 'Permit duration cannot exceed 30 days';
  }
  
  return null;
};

export const generateQRCodeData = (permitId: string, permitNumber: string): string => {
  return JSON.stringify({
    permitId,
    permitNumber,
    timestamp: Date.now(),
    version: '1.0'
  });
};
