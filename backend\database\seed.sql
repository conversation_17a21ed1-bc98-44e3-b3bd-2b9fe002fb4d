-- Seed data for development and testing
-- This file will be executed after the database is created

-- Insert default sites
INSERT INTO sites (id, name, location, timezone, contact_email, contact_phone, settings, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-************', 'Main Manufacturing Plant', 'Houston, TX', 'America/Chicago', '<EMAIL>', '******-0101', '{"maxPermitsPerDay": 50, "requireSupervisorApproval": true}', NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', 'Offshore Platform Alpha', 'Gulf of Mexico', 'America/Chicago', '<EMAIL>', '******-0102', '{"maxPermitsPerDay": 20, "requireSupervisorApproval": true, "weatherRestrictions": true}', NOW(), NOW()),
('************************************', 'Refinery Complex B', 'Baton Rouge, LA', 'America/Chicago', '<EMAIL>', '******-0103', '{"maxPermitsPerDay": 75, "requireSupervisorApproval": true, "hazmatRestrictions": true}', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert default users
INSERT INTO users (id, email, name, role, site_id, firebase_uid, is_active, created_at, updated_at) VALUES
-- Admin users
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'System Administrator', 'ADMIN', NULL, 'firebase_admin_uid_001', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'John Smith', 'ADMIN', '550e8400-e29b-41d4-a716-************', 'firebase_admin_uid_002', true, NOW(), NOW()),

-- Safety Officers
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Sarah Johnson', 'SAFETY_OFFICER', '550e8400-e29b-41d4-a716-************', 'firebase_safety_uid_001', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Mike Wilson', 'SAFETY_OFFICER', '550e8400-e29b-41d4-a716-************', 'firebase_safety_uid_002', true, NOW(), NOW()),

-- Supervisors
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'David Brown', 'SUPERVISOR', '550e8400-e29b-41d4-a716-************', 'firebase_supervisor_uid_001', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Lisa Davis', 'SUPERVISOR', '550e8400-e29b-41d4-a716-************', 'firebase_supervisor_uid_002', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Robert Miller', 'SUPERVISOR', '************************************', 'firebase_supervisor_uid_003', true, NOW(), NOW()),

-- Field Workers
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'James Anderson', 'FIELD_WORKER', '550e8400-e29b-41d4-a716-************', 'firebase_worker_uid_001', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Maria Garcia', 'FIELD_WORKER', '550e8400-e29b-41d4-a716-************', 'firebase_worker_uid_002', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Kevin Lee', 'FIELD_WORKER', '550e8400-e29b-41d4-a716-************', 'firebase_worker_uid_003', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Jennifer Taylor', 'FIELD_WORKER', '************************************', 'firebase_worker_uid_004', true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sample permits
INSERT INTO permits (
    id, permit_number, title, description, type, status, risk_level, location, 
    site_id, requested_by, supervisor_id, start_date, end_date, 
    emergency_contact, isolation_required, isolation_steps, ppe_required, safety_steps,
    qr_code, created_at, updated_at
) VALUES
-- Active permits
(
    '550e8400-e29b-41d4-a716-************',
    'HW-2024-001',
    'Welding on Main Pipeline',
    'Routine maintenance welding on the main gas pipeline section 4A. Requires hot work permit due to welding operations near pressurized systems.',
    'HOT_WORK',
    'ACTIVE',
    'HIGH',
    'Pipeline Section 4A, Main Plant',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    NOW() + INTERVAL '1 hour',
    NOW() + INTERVAL '8 hours',
    '******-EMERGENCY',
    true,
    '["Isolate gas supply", "Verify zero energy state", "Install blinds", "Test atmosphere"]',
    '["Fire-resistant coveralls", "Welding helmet", "Safety boots", "Heat-resistant gloves", "Gas monitor"]',
    '["Fire watch required", "Extinguisher on standby", "Atmospheric monitoring", "Emergency procedures briefed"]',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    NOW() - INTERVAL '2 hours',
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440101',
    'CS-2024-002',
    'Tank Inspection and Cleaning',
    'Annual inspection and cleaning of storage tank T-301. Confined space entry required for internal inspection.',
    'CONFINED_SPACE',
    'PENDING_APPROVAL',
    'CRITICAL',
    'Storage Tank T-301, Refinery Complex B',
    '************************************',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    NOW() + INTERVAL '1 day',
    NOW() + INTERVAL '2 days',
    '******-EMERGENCY',
    true,
    '["Drain and purge tank", "Verify atmosphere", "Install ventilation", "Test oxygen levels"]',
    '["SCBA", "Full body harness", "Chemical-resistant suit", "Safety boots", "Hard hat", "Gas monitor"]',
    '["Continuous atmospheric monitoring", "Attendant required", "Emergency retrieval system", "Communication system"]',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    NOW() - INTERVAL '1 hour',
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440102',
    'EL-2024-003',
    'Electrical Panel Maintenance',
    'Scheduled maintenance on main electrical distribution panel. Requires electrical isolation and lockout.',
    'ELECTRICAL',
    'APPROVED',
    'MEDIUM',
    'Electrical Room B, Main Plant',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    NOW() + INTERVAL '3 hours',
    NOW() + INTERVAL '6 hours',
    '******-EMERGENCY',
    true,
    '["De-energize circuits", "Apply lockout tags", "Verify zero energy", "Test with meter"]',
    '["Arc flash suit", "Insulated gloves", "Safety glasses", "Hard hat", "Insulated tools"]',
    '["Qualified electrician required", "Buddy system", "Emergency procedures", "First aid kit available"]',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    NOW() - INTERVAL '30 minutes',
    NOW()
)
ON CONFLICT (id) DO NOTHING;

-- Insert sample AI recommendations
INSERT INTO ai_recommendations (
    id, permit_id, type, recommendation, confidence, applied, created_at, updated_at
) VALUES
(
    '550e8400-e29b-41d4-a716-446655440200',
    '550e8400-e29b-41d4-a716-************',
    'PPE',
    'Consider adding flame-retardant coveralls for extended hot work operations near pressurized systems.',
    0.92,
    true,
    NOW() - INTERVAL '1 hour',
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440201',
    '550e8400-e29b-41d4-a716-************',
    'SAFETY_STEP',
    'Implement continuous atmospheric monitoring throughout the work duration due to proximity to gas systems.',
    0.89,
    true,
    NOW() - INTERVAL '1 hour',
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440202',
    '550e8400-e29b-41d4-a716-446655440101',
    'RISK_ASSESSMENT',
    'Recommend upgrading to CRITICAL risk level due to confined space entry in chemical storage tank.',
    0.95,
    false,
    NOW() - INTERVAL '30 minutes',
    NOW()
)
ON CONFLICT (id) DO NOTHING;

-- Insert sample audit logs
INSERT INTO audit_logs (
    id, user_id, entity_type, entity_id, action, changes, timestamp, ip_address, user_agent
) VALUES
(
    '550e8400-e29b-41d4-a716-446655440300',
    '550e8400-e29b-41d4-a716-************',
    'Permit',
    '550e8400-e29b-41d4-a716-************',
    'CREATE_PERMIT',
    '{"created": {"title": "Welding on Main Pipeline", "type": "HOT_WORK", "status": "DRAFT"}}',
    NOW() - INTERVAL '2 hours',
    '*************',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15'
),
(
    '550e8400-e29b-41d4-a716-446655440301',
    '550e8400-e29b-41d4-a716-************',
    'Permit',
    '550e8400-e29b-41d4-a716-************',
    'APPROVE_PERMIT',
    '{"before": {"status": "PENDING_APPROVAL"}, "after": {"status": "APPROVED"}, "approved": true}',
    NOW() - INTERVAL '1 hour',
    '************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
)
ON CONFLICT (id) DO NOTHING;
