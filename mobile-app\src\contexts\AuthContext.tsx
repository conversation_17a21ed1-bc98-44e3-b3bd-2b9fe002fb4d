import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { initializeApp } from 'firebase/app';
import {
  getAuth,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
} from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '@epermit/shared';
import { apiService } from '../services/api';

// Firebase configuration
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setFirebaseUser(firebaseUser);
      
      if (firebaseUser) {
        try {
          // Get ID token
          const token = await firebaseUser.getIdToken();
          
          // Store token for API calls
          await AsyncStorage.setItem('authToken', token);
          
          // Get user profile from backend
          const userProfile = await apiService.getCurrentUser();
          setUser(userProfile);
          
          // Store user data locally
          await AsyncStorage.setItem('userData', JSON.stringify(userProfile));
        } catch (error) {
          console.error('Error fetching user profile:', error);
          // Try to load cached user data
          try {
            const cachedUserData = await AsyncStorage.getItem('userData');
            if (cachedUserData) {
              setUser(JSON.parse(cachedUserData));
            }
          } catch (cacheError) {
            console.error('Error loading cached user data:', cacheError);
          }
        }
      } else {
        setUser(null);
        await AsyncStorage.multiRemove(['authToken', 'userData']);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string): Promise<void> => {
    try {
      setLoading(true);
      await signInWithEmailAndPassword(auth, email, password);
      // User state will be updated by onAuthStateChanged
    } catch (error: any) {
      setLoading(false);
      throw new Error(error.message || 'Sign in failed');
    }
  };

  const signOutUser = async (): Promise<void> => {
    try {
      setLoading(true);
      await signOut(auth);
      // User state will be updated by onAuthStateChanged
    } catch (error: any) {
      setLoading(false);
      throw new Error(error.message || 'Sign out failed');
    }
  };

  const refreshUser = async (): Promise<void> => {
    if (firebaseUser) {
      try {
        // Force token refresh
        const token = await firebaseUser.getIdToken(true);
        await AsyncStorage.setItem('authToken', token);
        
        // Fetch updated user profile
        const userProfile = await apiService.getCurrentUser();
        setUser(userProfile);
        
        // Update cached user data
        await AsyncStorage.setItem('userData', JSON.stringify(userProfile));
      } catch (error) {
        console.error('Error refreshing user:', error);
      }
    }
  };

  const value: AuthContextType = {
    user,
    firebaseUser,
    loading,
    signIn,
    signOut: signOutUser,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
