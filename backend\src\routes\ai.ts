import { Router } from 'express';
import {
  generateAIRecommendations,
  getAIRecommendations,
  applyAIRecommendation,
  getAIInsights,
} from '../controllers/aiController';
import { authenticateToken, requireRole } from '../middleware/auth';
import { validateUUID } from '../middleware/validation';
import { auditLog, auditMiddleware } from '../middleware/audit';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// Generate AI recommendations for a permit
router.post(
  '/permits/:permitId/recommendations',
  validateUUID,
  auditLog('AIRecommendation', 'GENERATE_AI_RECOMMENDATIONS'),
  auditMiddleware,
  generateAIRecommendations
);

// Get AI recommendations for a permit
router.get(
  '/permits/:permitId/recommendations',
  validateUUID,
  getAIRecommendations
);

// Apply an AI recommendation
router.post(
  '/recommendations/:id/apply',
  validateUUID,
  requireRole(['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN']),
  auditLog('AIRecommendation', 'APPLY_AI_RECOMMENDATION'),
  auditMiddleware,
  applyAIRecommendation
);

// Get AI insights and analytics
router.get(
  '/insights',
  requireRole(['SAFETY_OFFICER', 'ADMIN']),
  getAIInsights
);

export default router;
