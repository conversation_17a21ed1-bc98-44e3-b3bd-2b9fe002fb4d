import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { Permit, User, ApiResponse, PermitAnalytics, SiteAnalytics } from '@epermit/shared';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v1';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('authToken');
          localStorage.removeItem('userData');
          window.location.href = '/login';
        }
        
        const message = error.response?.data?.error || error.message || 'Network error';
        throw new Error(message);
      }
    );
  }

  // Auth endpoints
  async getCurrentUser(): Promise<User> {
    const response: ApiResponse<User> = await this.api.get('/auth/me');
    return response.data!;
  }

  async getUserPermissions(): Promise<any> {
    const response: ApiResponse = await this.api.get('/auth/permissions');
    return response.data;
  }

  async registerUser(userData: Partial<User>): Promise<User> {
    const response: ApiResponse<User> = await this.api.post('/auth/register', userData);
    return response.data!;
  }

  // Permit endpoints
  async getPermits(params?: {
    page?: number;
    limit?: number;
    status?: string;
    type?: string;
    riskLevel?: string;
    siteId?: string;
    requestedBy?: string;
    startDate?: string;
    endDate?: string;
    sortBy?: string;
    sortOrder?: string;
  }): Promise<{ permits: Permit[]; pagination: any }> {
    const response: ApiResponse = await this.api.get('/permits', { params });
    return response.data!;
  }

  async getPermitById(id: string): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.get(`/permits/${id}`);
    return response.data!;
  }

  async createPermit(permitData: Partial<Permit>): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.post('/permits', permitData);
    return response.data!;
  }

  async updatePermit(id: string, updates: Partial<Permit>): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.put(`/permits/${id}`, updates);
    return response.data!;
  }

  async approvePermit(id: string, data: {
    approved: boolean;
    notes?: string;
    rejectionReason?: string;
  }): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.post(`/permits/${id}/approve`, data);
    return response.data!;
  }

  async submitPermitForApproval(id: string): Promise<Permit> {
    const response: ApiResponse<Permit> = await this.api.post(`/permits/${id}/submit`);
    return response.data!;
  }

  async deletePermit(id: string): Promise<void> {
    await this.api.delete(`/permits/${id}`);
  }

  // AI endpoints
  async generateAIRecommendations(permitId: string, permitData: any): Promise<any[]> {
    const response: ApiResponse = await this.api.post(
      `/ai/permits/${permitId}/recommendations`,
      permitData
    );
    return response.data!;
  }

  async getAIRecommendations(permitId: string, params?: {
    type?: string;
    applied?: boolean;
  }): Promise<any[]> {
    const response: ApiResponse = await this.api.get(
      `/ai/permits/${permitId}/recommendations`,
      { params }
    );
    return response.data!;
  }

  async applyAIRecommendation(recommendationId: string): Promise<any> {
    const response: ApiResponse = await this.api.post(
      `/ai/recommendations/${recommendationId}/apply`
    );
    return response.data!;
  }

  async getAIInsights(params?: {
    siteId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<any> {
    const response: ApiResponse = await this.api.get('/ai/insights', { params });
    return response.data!;
  }

  // Analytics endpoints
  async getPermitAnalytics(params?: {
    siteId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<PermitAnalytics> {
    const response: ApiResponse<PermitAnalytics> = await this.api.get('/analytics/permits', { params });
    return response.data!;
  }

  async getSiteAnalytics(): Promise<SiteAnalytics[]> {
    const response: ApiResponse<SiteAnalytics[]> = await this.api.get('/analytics/sites');
    return response.data!;
  }

  async getKPIDashboard(params?: { siteId?: string }): Promise<any> {
    const response: ApiResponse = await this.api.get('/analytics/kpis', { params });
    return response.data!;
  }

  async getAuditTrail(params?: {
    page?: number;
    limit?: number;
    entityType?: string;
    entityId?: string;
    action?: string;
    userId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{ auditLogs: any[]; pagination: any }> {
    const response: ApiResponse = await this.api.get('/analytics/audit', { params });
    return response.data!;
  }

  // User management endpoints
  async getUsers(params?: {
    page?: number;
    limit?: number;
    role?: string;
    siteId?: string;
    isActive?: boolean;
  }): Promise<{ users: User[]; pagination: any }> {
    const response: ApiResponse = await this.api.get('/users', { params });
    return response.data!;
  }

  async getUserById(id: string): Promise<User> {
    const response: ApiResponse<User> = await this.api.get(`/users/${id}`);
    return response.data!;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    const response: ApiResponse<User> = await this.api.put(`/users/${id}`, updates);
    return response.data!;
  }

  async deleteUser(id: string): Promise<void> {
    await this.api.delete(`/users/${id}`);
  }

  // Site management endpoints
  async getSites(params?: {
    page?: number;
    limit?: number;
    isActive?: boolean;
  }): Promise<{ sites: any[]; pagination: any }> {
    const response: ApiResponse = await this.api.get('/sites', { params });
    return response.data!;
  }

  async getSiteById(id: string): Promise<any> {
    const response: ApiResponse = await this.api.get(`/sites/${id}`);
    return response.data!;
  }

  async createSite(siteData: any): Promise<any> {
    const response: ApiResponse = await this.api.post('/sites', siteData);
    return response.data!;
  }

  async updateSite(id: string, updates: any): Promise<any> {
    const response: ApiResponse = await this.api.put(`/sites/${id}`, updates);
    return response.data!;
  }

  async deleteSite(id: string): Promise<void> {
    await this.api.delete(`/sites/${id}`);
  }

  // File upload endpoints
  async uploadFile(file: File): Promise<{ url: string; filename: string }> {
    const formData = new FormData();
    formData.append('file', file);

    const response: ApiResponse = await this.api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data!;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.api.get('/health');
      return true;
    } catch (error) {
      return false;
    }
  }
}

export const apiService = new ApiService();
