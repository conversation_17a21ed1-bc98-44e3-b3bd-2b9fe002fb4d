import { Response } from 'express';
import { AIRecommendation, Permit } from '../models';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { createAuditLog } from '../middleware/audit';
import { PermitType, getDefaultPPE, getDefaultSafetySteps } from '@epermit/shared';

// Mock AI service - replace with actual OpenAI integration later
class MockAIService {
  static async generateRecommendations(permitData: {
    type: PermitType;
    location: string;
    description: string;
    isolationRequired: boolean;
  }) {
    const recommendations = [];

    // PPE recommendations based on permit type and location
    const ppeRecommendations = this.getPPERecommendations(permitData.type, permitData.location);
    recommendations.push(...ppeRecommendations);

    // Safety step recommendations
    const safetyRecommendations = this.getSafetyRecommendations(permitData.type, permitData.description);
    recommendations.push(...safetyRecommendations);

    // Isolation recommendations
    if (permitData.isolationRequired) {
      const isolationRecommendations = this.getIsolationRecommendations(permitData.type);
      recommendations.push(...isolationRecommendations);
    }

    // Risk assessment recommendations
    const riskRecommendations = this.getRiskAssessmentRecommendations(permitData);
    recommendations.push(...riskRecommendations);

    return recommendations;
  }

  private static getPPERecommendations(type: PermitType, location: string) {
    const recommendations = [];
    
    if (type === 'HOT_WORK') {
      recommendations.push({
        type: 'PPE',
        recommendation: 'Consider flame-retardant coveralls for extended hot work operations',
        confidence: 0.9,
      });
      
      if (location.toLowerCase().includes('confined')) {
        recommendations.push({
          type: 'PPE',
          recommendation: 'Additional ventilation equipment required for confined space hot work',
          confidence: 0.95,
        });
      }
    }

    if (type === 'ELECTRICAL' && location.toLowerCase().includes('wet')) {
      recommendations.push({
        type: 'PPE',
        recommendation: 'Waterproof electrical gloves and boots required for wet conditions',
        confidence: 0.92,
      });
    }

    if (type === 'HEIGHT_WORK') {
      recommendations.push({
        type: 'PPE',
        recommendation: 'Double lanyard system recommended for heights above 6 meters',
        confidence: 0.88,
      });
    }

    return recommendations;
  }

  private static getSafetyRecommendations(type: PermitType, description: string) {
    const recommendations = [];
    
    if (type === 'CONFINED_SPACE') {
      recommendations.push({
        type: 'SAFETY_STEP',
        recommendation: 'Implement continuous atmospheric monitoring throughout the work',
        confidence: 0.96,
      });
      
      if (description.toLowerCase().includes('welding')) {
        recommendations.push({
          type: 'SAFETY_STEP',
          recommendation: 'Increase ventilation rate by 50% when welding in confined spaces',
          confidence: 0.94,
        });
      }
    }

    if (type === 'CHEMICAL_HANDLING') {
      recommendations.push({
        type: 'SAFETY_STEP',
        recommendation: 'Establish decontamination station at work area exit',
        confidence: 0.91,
      });
    }

    return recommendations;
  }

  private static getIsolationRecommendations(type: PermitType) {
    const recommendations = [];
    
    if (type === 'ELECTRICAL') {
      recommendations.push({
        type: 'ISOLATION',
        recommendation: 'Verify zero energy state with calibrated test equipment',
        confidence: 0.98,
      });
    }

    if (type === 'MAINTENANCE') {
      recommendations.push({
        type: 'ISOLATION',
        recommendation: 'Consider mechanical isolation in addition to electrical lockout',
        confidence: 0.85,
      });
    }

    return recommendations;
  }

  private static getRiskAssessmentRecommendations(permitData: any) {
    const recommendations = [];
    
    if (permitData.type === 'HOT_WORK' && permitData.location.toLowerCase().includes('storage')) {
      recommendations.push({
        type: 'RISK_ASSESSMENT',
        recommendation: 'Conduct fire risk assessment of surrounding storage materials',
        confidence: 0.93,
      });
    }

    if (permitData.isolationRequired && permitData.type === 'MAINTENANCE') {
      recommendations.push({
        type: 'RISK_ASSESSMENT',
        recommendation: 'Review startup procedures to prevent unexpected equipment activation',
        confidence: 0.87,
      });
    }

    return recommendations;
  }
}

export const generateAIRecommendations = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { permitId } = req.params;
  const { type, location, description, isolationRequired } = req.body;

  // Verify permit exists and user has access
  const permit = await Permit.findByPk(permitId);
  if (!permit) {
    throw createError('Permit not found', 404);
  }

  // Check site access
  if (req.user.role !== 'ADMIN' && req.user.siteId !== permit.siteId) {
    throw createError('Access denied to this permit', 403);
  }

  // Generate AI recommendations
  const aiRecommendations = await MockAIService.generateRecommendations({
    type: type || permit.type,
    location: location || permit.location,
    description: description || permit.description,
    isolationRequired: isolationRequired !== undefined ? isolationRequired : permit.isolationRequired,
  });

  // Save recommendations to database
  const savedRecommendations = [];
  for (const rec of aiRecommendations) {
    const recommendation = await AIRecommendation.create({
      permitId,
      type: rec.type,
      recommendation: rec.recommendation,
      confidence: rec.confidence,
    });
    savedRecommendations.push(recommendation);
  }

  await createAuditLog(
    req.user.id,
    'AIRecommendation',
    permitId,
    'GENERATE_AI_RECOMMENDATIONS',
    {
      permitId,
      recommendationsCount: savedRecommendations.length,
      recommendations: savedRecommendations.map(r => r.toJSON()),
    },
    req.ip,
    req.get('User-Agent')
  );

  res.json({
    success: true,
    data: savedRecommendations,
    message: `Generated ${savedRecommendations.length} AI recommendations`,
  });
});

export const getAIRecommendations = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { permitId } = req.params;
  const { type, applied } = req.query;

  // Verify permit exists and user has access
  const permit = await Permit.findByPk(permitId);
  if (!permit) {
    throw createError('Permit not found', 404);
  }

  // Check site access
  if (req.user.role !== 'ADMIN' && req.user.siteId !== permit.siteId) {
    throw createError('Access denied to this permit', 403);
  }

  const where: any = { permitId };
  if (type) where.type = type;
  if (applied !== undefined) where.applied = applied === 'true';

  const recommendations = await AIRecommendation.findAll({
    where,
    order: [['confidence', 'DESC'], ['createdAt', 'DESC']],
  });

  res.json({
    success: true,
    data: recommendations,
  });
});

export const applyAIRecommendation = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  const recommendation = await AIRecommendation.findByPk(id, {
    include: [{ model: Permit, as: 'permit' }],
  });

  if (!recommendation) {
    throw createError('Recommendation not found', 404);
  }

  // Check permissions
  if (!['SUPERVISOR', 'SAFETY_OFFICER', 'ADMIN'].includes(req.user.role)) {
    throw createError('Permission denied to apply recommendations', 403);
  }

  // Check site access
  if (req.user.role !== 'ADMIN' && req.user.siteId !== recommendation.permit.siteId) {
    throw createError('Access denied to this permit', 403);
  }

  if (recommendation.applied) {
    throw createError('Recommendation already applied', 400);
  }

  await recommendation.update({
    applied: true,
    appliedBy: req.user.id,
    appliedAt: new Date(),
  });

  await createAuditLog(
    req.user.id,
    'AIRecommendation',
    recommendation.id,
    'APPLY_AI_RECOMMENDATION',
    {
      recommendationId: recommendation.id,
      permitId: recommendation.permitId,
      recommendation: recommendation.recommendation,
      type: recommendation.type,
    },
    req.ip,
    req.get('User-Agent')
  );

  res.json({
    success: true,
    data: recommendation,
    message: 'Recommendation applied successfully',
  });
});

export const getAIInsights = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  // Check permissions
  if (!['SAFETY_OFFICER', 'ADMIN'].includes(req.user.role)) {
    throw createError('Permission denied to view AI insights', 403);
  }

  const { siteId, startDate, endDate } = req.query;

  const where: any = {};
  if (siteId) where.siteId = siteId;
  if (req.user.role !== 'ADMIN') where.siteId = req.user.siteId;

  // Get recommendation statistics
  const totalRecommendations = await AIRecommendation.count({
    include: [{
      model: Permit,
      as: 'permit',
      where,
    }],
  });

  const appliedRecommendations = await AIRecommendation.count({
    where: { applied: true },
    include: [{
      model: Permit,
      as: 'permit',
      where,
    }],
  });

  // Get recommendations by type
  const recommendationsByType = await AIRecommendation.findAll({
    attributes: [
      'type',
      [AIRecommendation.sequelize!.fn('COUNT', AIRecommendation.sequelize!.col('AIRecommendation.id')), 'count'],
      [AIRecommendation.sequelize!.fn('AVG', AIRecommendation.sequelize!.col('confidence')), 'avgConfidence'],
    ],
    include: [{
      model: Permit,
      as: 'permit',
      where,
      attributes: [],
    }],
    group: ['type'],
    raw: true,
  });

  // Get top recommendations by confidence
  const topRecommendations = await AIRecommendation.findAll({
    include: [{
      model: Permit,
      as: 'permit',
      where,
      attributes: ['id', 'permitNumber', 'title', 'type'],
    }],
    order: [['confidence', 'DESC']],
    limit: 10,
  });

  const insights = {
    summary: {
      totalRecommendations,
      appliedRecommendations,
      applicationRate: totalRecommendations > 0 ? (appliedRecommendations / totalRecommendations) * 100 : 0,
    },
    byType: recommendationsByType,
    topRecommendations,
  };

  res.json({
    success: true,
    data: insights,
  });
});
