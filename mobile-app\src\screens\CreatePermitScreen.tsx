import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  HelperText,
  Chip,
  Surface,
  IconButton,
} from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import DateTimePicker from 'react-native-date-picker';
import RNPickerSelect from 'react-native-picker-select';
import Toast from 'react-native-toast-message';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useNetwork } from '../contexts/NetworkContext';
import { colors, spacing, typography } from '../theme';
import { apiService } from '../services/api';
import { PermitType, getDefaultPPE, getDefaultSafetySteps, generatePermitNumber } from '@epermit/shared';
import VoiceRecorder from '../components/VoiceRecorder';

interface CreatePermitForm {
  title: string;
  description: string;
  type: PermitType;
  location: string;
  startDate: Date;
  endDate: Date;
  emergencyContact: string;
  supervisorId?: string;
  isolationRequired: boolean;
}

export default function CreatePermitScreen({ navigation }: any) {
  const { user } = useAuth();
  const { isConnected } = useNetwork();
  const [loading, setLoading] = useState(false);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [ppeItems, setPpeItems] = useState<any[]>([]);
  const [safetySteps, setSafetySteps] = useState<any[]>([]);
  const [voiceNotes, setVoiceNotes] = useState<string[]>([]);
  const [aiSuggestions, setAiSuggestions] = useState<any[]>([]);
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreatePermitForm>({
    defaultValues: {
      startDate: new Date(),
      endDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      isolationRequired: false,
    },
  });

  const watchedType = watch('type');

  useEffect(() => {
    if (watchedType) {
      const defaultPPE = getDefaultPPE(watchedType);
      const defaultSteps = getDefaultSafetySteps(watchedType);
      setPpeItems(defaultPPE);
      setSafetySteps(defaultSteps);
      
      // Generate AI suggestions if online
      if (isConnected) {
        generateAISuggestions();
      }
    }
  }, [watchedType, isConnected]);

  const generateAISuggestions = async () => {
    try {
      const formData = watch();
      if (formData.type && formData.location && formData.description) {
        const suggestions = await apiService.generateAIRecommendations('temp-id', {
          type: formData.type,
          location: formData.location,
          description: formData.description,
          isolationRequired: formData.isolationRequired,
        });
        setAiSuggestions(suggestions);
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
    }
  };

  const onSubmit = async (data: CreatePermitForm) => {
    try {
      setLoading(true);

      const permitData = {
        ...data,
        siteId: user?.siteId!,
        ppeRequired: ppeItems,
        safetySteps: safetySteps,
        voiceNotes: voiceNotes,
      };

      if (isConnected) {
        // Create permit online
        const permit = await apiService.createPermit(permitData);
        Toast.show({
          type: 'success',
          text1: 'Permit Created',
          text2: `Permit #${permit.permitNumber} created successfully`,
        });
        navigation.goBack();
      } else {
        // Save permit offline
        // This would be handled by the offline storage service
        Toast.show({
          type: 'info',
          text1: 'Permit Saved Offline',
          text2: 'Permit will be synced when connection is restored',
        });
        navigation.goBack();
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Failed to Create Permit',
        text2: error.message || 'Please try again',
      });
    } finally {
      setLoading(false);
    }
  };

  const togglePPEItem = (index: number) => {
    const updatedPPE = [...ppeItems];
    updatedPPE[index].checked = !updatedPPE[index].checked;
    setPpeItems(updatedPPE);
  };

  const toggleSafetyStep = (index: number) => {
    const updatedSteps = [...safetySteps];
    updatedSteps[index].completed = !updatedSteps[index].completed;
    setSafetySteps(updatedSteps);
  };

  const addVoiceNote = (audioPath: string) => {
    setVoiceNotes([...voiceNotes, audioPath]);
    setShowVoiceRecorder(false);
  };

  const permitTypes = [
    { label: 'Hot Work', value: 'HOT_WORK' },
    { label: 'Confined Space', value: 'CONFINED_SPACE' },
    { label: 'Electrical Work', value: 'ELECTRICAL' },
    { label: 'Height Work', value: 'HEIGHT_WORK' },
    { label: 'Excavation', value: 'EXCAVATION' },
    { label: 'Chemical Handling', value: 'CHEMICAL_HANDLING' },
    { label: 'Maintenance', value: 'MAINTENANCE' },
    { label: 'Other', value: 'OTHER' },
  ];

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Basic Information</Text>

          <Controller
            control={control}
            name="title"
            rules={{ required: 'Title is required' }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Permit Title"
                mode="outlined"
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                error={!!errors.title}
                style={styles.input}
              />
            )}
          />
          <HelperText type="error" visible={!!errors.title}>
            {errors.title?.message}
          </HelperText>

          <Controller
            control={control}
            name="description"
            rules={{ required: 'Description is required' }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Description"
                mode="outlined"
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                multiline
                numberOfLines={3}
                error={!!errors.description}
                style={styles.input}
              />
            )}
          />
          <HelperText type="error" visible={!!errors.description}>
            {errors.description?.message}
          </HelperText>

          <Text style={styles.fieldLabel}>Permit Type</Text>
          <Controller
            control={control}
            name="type"
            rules={{ required: 'Permit type is required' }}
            render={({ field: { onChange, value } }) => (
              <RNPickerSelect
                onValueChange={onChange}
                items={permitTypes}
                value={value}
                placeholder={{ label: 'Select permit type...', value: null }}
                style={{
                  inputIOS: styles.picker,
                  inputAndroid: styles.picker,
                }}
              />
            )}
          />
          <HelperText type="error" visible={!!errors.type}>
            {errors.type?.message}
          </HelperText>

          <Controller
            control={control}
            name="location"
            rules={{ required: 'Location is required' }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Work Location"
                mode="outlined"
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                error={!!errors.location}
                style={styles.input}
              />
            )}
          />
          <HelperText type="error" visible={!!errors.location}>
            {errors.location?.message}
          </HelperText>

          <View style={styles.dateContainer}>
            <View style={styles.dateField}>
              <Text style={styles.fieldLabel}>Start Date</Text>
              <Button
                mode="outlined"
                onPress={() => setShowStartDatePicker(true)}
                style={styles.dateButton}
              >
                {watch('startDate').toLocaleDateString()}
              </Button>
            </View>
            <View style={styles.dateField}>
              <Text style={styles.fieldLabel}>End Date</Text>
              <Button
                mode="outlined"
                onPress={() => setShowEndDatePicker(true)}
                style={styles.dateButton}
              >
                {watch('endDate').toLocaleDateString()}
              </Button>
            </View>
          </View>

          <Controller
            control={control}
            name="emergencyContact"
            rules={{ required: 'Emergency contact is required' }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Emergency Contact"
                mode="outlined"
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                keyboardType="phone-pad"
                error={!!errors.emergencyContact}
                style={styles.input}
              />
            )}
          />
          <HelperText type="error" visible={!!errors.emergencyContact}>
            {errors.emergencyContact?.message}
          </HelperText>
        </Card.Content>
      </Card>

      {/* PPE Requirements */}
      {ppeItems.length > 0 && (
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>PPE Requirements</Text>
            {ppeItems.map((item, index) => (
              <Surface key={item.id} style={styles.checklistItem}>
                <View style={styles.checklistContent}>
                  <Text style={styles.checklistText}>{item.name}</Text>
                  {item.required && (
                    <Chip size="small" mode="outlined" textStyle={styles.requiredChip}>
                      Required
                    </Chip>
                  )}
                </View>
                <IconButton
                  icon={item.checked ? 'checkbox-marked' : 'checkbox-blank-outline'}
                  iconColor={item.checked ? colors.success : colors.gray[400]}
                  onPress={() => togglePPEItem(index)}
                />
              </Surface>
            ))}
          </Card.Content>
        </Card>
      )}

      {/* Voice Notes */}
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Voice Notes</Text>
            <IconButton
              icon="microphone"
              iconColor={colors.primary}
              onPress={() => setShowVoiceRecorder(true)}
            />
          </View>
          {voiceNotes.length === 0 ? (
            <Text style={styles.emptyText}>No voice notes added</Text>
          ) : (
            voiceNotes.map((note, index) => (
              <Surface key={index} style={styles.voiceNoteItem}>
                <Ionicons name="mic" size={16} color={colors.primary} />
                <Text style={styles.voiceNoteText}>Voice note {index + 1}</Text>
              </Surface>
            ))
          )}
        </Card.Content>
      </Card>

      {/* AI Suggestions */}
      {aiSuggestions.length > 0 && (
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>AI Suggestions</Text>
            {aiSuggestions.map((suggestion, index) => (
              <Surface key={index} style={styles.suggestionItem}>
                <View style={styles.suggestionHeader}>
                  <Ionicons name="bulb" size={16} color={colors.warning} />
                  <Text style={styles.suggestionType}>{suggestion.type}</Text>
                  <Text style={styles.suggestionConfidence}>
                    {Math.round(suggestion.confidence * 100)}%
                  </Text>
                </View>
                <Text style={styles.suggestionText}>{suggestion.recommendation}</Text>
              </Surface>
            ))}
          </Card.Content>
        </Card>
      )}

      <View style={styles.buttonContainer}>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.cancelButton}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSubmit(onSubmit)}
          loading={loading}
          disabled={loading}
          style={styles.submitButton}
        >
          Create Permit
        </Button>
      </View>

      {/* Date Pickers */}
      <DateTimePicker
        modal
        open={showStartDatePicker}
        date={watch('startDate')}
        onConfirm={(date) => {
          setShowStartDatePicker(false);
          setValue('startDate', date);
        }}
        onCancel={() => setShowStartDatePicker(false)}
        minimumDate={new Date()}
      />

      <DateTimePicker
        modal
        open={showEndDatePicker}
        date={watch('endDate')}
        onConfirm={(date) => {
          setShowEndDatePicker(false);
          setValue('endDate', date);
        }}
        onCancel={() => setShowEndDatePicker(false)}
        minimumDate={watch('startDate')}
      />

      {/* Voice Recorder Modal */}
      {showVoiceRecorder && (
        <VoiceRecorder
          visible={showVoiceRecorder}
          onClose={() => setShowVoiceRecorder(false)}
          onSave={addVoiceNote}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  card: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: spacing.md,
    color: colors.text,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  input: {
    marginBottom: spacing.xs,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  picker: {
    fontSize: 16,
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 4,
    color: colors.text,
    paddingRight: 30,
    backgroundColor: colors.white,
  },
  dateContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  dateField: {
    flex: 1,
  },
  dateButton: {
    justifyContent: 'flex-start',
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderRadius: 8,
  },
  checklistContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  checklistText: {
    fontSize: 14,
    color: colors.text,
    flex: 1,
  },
  requiredChip: {
    fontSize: 10,
    color: colors.error,
  },
  emptyText: {
    fontSize: 14,
    color: colors.gray[500],
    textAlign: 'center',
    paddingVertical: spacing.lg,
  },
  voiceNoteItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderRadius: 8,
  },
  voiceNoteText: {
    marginLeft: spacing.sm,
    fontSize: 14,
    color: colors.text,
  },
  suggestionItem: {
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.warning,
  },
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  suggestionType: {
    marginLeft: spacing.xs,
    fontSize: 12,
    fontWeight: '600',
    color: colors.warning,
    flex: 1,
  },
  suggestionConfidence: {
    fontSize: 12,
    color: colors.gray[500],
  },
  suggestionText: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    padding: spacing.lg,
    paddingBottom: spacing.xxl,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 1,
  },
});
