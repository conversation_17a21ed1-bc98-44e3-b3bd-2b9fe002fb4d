import { Request, Response, NextFunction } from 'express';
import { AuditLog } from '../models';
import { AuthenticatedRequest } from './auth';

interface AuditableRequest extends AuthenticatedRequest {
  auditData?: {
    entityType: string;
    entityId: string;
    action: string;
    originalData?: any;
  };
}

export const auditLog = (entityType: string, action: string) => {
  return async (req: AuditableRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Store audit data for later use
      req.auditData = {
        entityType,
        entityId: req.params.id || 'unknown',
        action,
      };

      // For update operations, capture original data
      if (action.includes('UPDATE') || action.includes('DELETE')) {
        // This would be implemented based on the specific entity
        // For now, we'll capture it in the controller
      }

      next();
    } catch (error) {
      console.error('Audit middleware error:', error);
      next();
    }
  };
};

export const createAuditLog = async (
  userId: string,
  entityType: string,
  entityId: string,
  action: string,
  changes: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): Promise<void> => {
  try {
    await AuditLog.create({
      userId,
      entityType,
      entityId,
      action,
      changes,
      timestamp: new Date(),
      ipAddress,
      userAgent,
    });
  } catch (error) {
    console.error('Failed to create audit log:', error);
    // Don't throw error to avoid breaking the main operation
  }
};

export const auditMiddleware = async (
  req: AuditableRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  // Override res.json to capture response data for audit
  const originalJson = res.json;
  
  res.json = function(data: any) {
    // Create audit log after successful operation
    if (req.user && req.auditData && res.statusCode < 400) {
      const changes = {
        request: {
          method: req.method,
          url: req.originalUrl,
          body: req.body,
          params: req.params,
          query: req.query,
        },
        response: {
          statusCode: res.statusCode,
          data: data?.data || data,
        },
      };

      createAuditLog(
        req.user.id,
        req.auditData.entityType,
        req.auditData.entityId,
        req.auditData.action,
        changes,
        req.ip,
        req.get('User-Agent')
      );
    }

    return originalJson.call(this, data);
  };

  next();
};
