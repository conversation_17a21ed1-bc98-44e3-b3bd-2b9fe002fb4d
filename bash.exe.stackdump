Stack trace:
Frame         Function      Args
0007FFFF9BE0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF9BE0, 0007FFFF8AE0) msys-2.0.dll+0x2118E
0007FFFF9BE0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9BE0  0002100469F2 (00021028DF99, 0007FFFF9A98, 0007FFFF9BE0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9BE0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9BE0  00021006A545 (0007FFFF9BF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9BF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD6C240000 ntdll.dll
7FFD69FE0000 KERNEL32.DLL
7FFD69870000 KERNELBASE.dll
7FFD6B230000 USER32.dll
7FFD69FB0000 win32u.dll
7FFD6B7C0000 GDI32.dll
7FFD69E70000 gdi32full.dll
7FFD695D0000 msvcp_win.dll
7FFD69680000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD6BA60000 advapi32.dll
7FFD6B4B0000 msvcrt.dll
7FFD6B400000 sechost.dll
7FFD6B870000 RPCRT4.dll
7FFD68990000 CRYPTBASE.DLL
7FFD697D0000 bcryptPrimitives.dll
7FFD6AF40000 IMM32.DLL
