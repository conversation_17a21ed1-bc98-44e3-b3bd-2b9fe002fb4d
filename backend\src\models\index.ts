import sequelize from '../config/database';
import User from './User';
import Site from './Site';
import Permit from './Permit';
import AuditLog from './AuditLog';
import AIRecommendation from './AIRecommendation';

// Define associations
User.belongsTo(Site, { foreignKey: 'siteId', as: 'site' });
Site.hasMany(User, { foreignKey: 'siteId', as: 'users' });

Permit.belongsTo(Site, { foreignKey: 'siteId', as: 'site' });
Site.hasMany(Permit, { foreignKey: 'siteId', as: 'permits' });

Permit.belongsTo(User, { foreignKey: 'requestedBy', as: 'requester' });
Permit.belongsTo(User, { foreignKey: 'approvedBy', as: 'approver' });
Permit.belongsTo(User, { foreignKey: 'supervisorId', as: 'supervisor' });

User.hasMany(Permit, { foreignKey: 'requestedBy', as: 'requestedPermits' });
User.hasMany(Permit, { foreignKey: 'approvedBy', as: 'approvedPermits' });
User.hasMany(Permit, { foreignKey: 'supervisorId', as: 'supervisedPermits' });

AIRecommendation.belongsTo(Permit, { foreignKey: 'permitId', as: 'permit' });
Permit.hasMany(AIRecommendation, { foreignKey: 'permitId', as: 'recommendations' });

AIRecommendation.belongsTo(User, { foreignKey: 'appliedBy', as: 'appliedByUser' });
User.hasMany(AIRecommendation, { foreignKey: 'appliedBy', as: 'appliedRecommendations' });

AuditLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });
User.hasMany(AuditLog, { foreignKey: 'userId', as: 'auditLogs' });

export {
  sequelize,
  User,
  Site,
  Permit,
  AuditLog,
  AIRecommendation,
};

export default {
  sequelize,
  User,
  Site,
  Permit,
  AuditLog,
  AIRecommendation,
};
